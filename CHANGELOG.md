#  (2025-06-19)


### Bug Fixes

* add proper output variables for ECR repositories ([d89f61a](https://bitbucket.org/surveysparrow/sparrow-tofu-modules/commits/d89f61acd93854fb596ee350fe49e0606578ca4e))
* remove all provider files causes issue with terragrunt ([ef7d0a0](https://bitbucket.org/surveysparrow/sparrow-tofu-modules/commits/ef7d0a04d64874a8394497818835a07378df0049))


### Features

* add ECR module in opentofu ([88edfff](https://bitbucket.org/surveysparrow/sparrow-tofu-modules/commits/88edfffc413952cf8f68c31e92aa88e86776228d))
* add ECR module support ([51234aa](https://bitbucket.org/surveysparrow/sparrow-tofu-modules/commits/51234aac5282f886f029b8e73871d59daacaaa1f))
* add ecr to reputation management ([c970b49](https://bitbucket.org/surveysparrow/sparrow-tofu-modules/commits/c970b4969748830fe1ce3053227907117df89a84))
* add reputation module - initial version with a few queues ([ac483ec](https://bitbucket.org/surveysparrow/sparrow-tofu-modules/commits/ac483ecc2f90f7e4f9e66c7299f209f2179b1cc6))


### Reverts

* Revert "modify acm module to create one resource" ([85bc27d](https://bitbucket.org/surveysparrow/sparrow-tofu-modules/commits/85bc27d04ea302664c44aed94b410425a49accba))



