# This is a sample service to show how to use the terragrunt configurations
# This is not used to create any resources, it's just a sample.

module "sqs" {
  source = "../../../components/aws/sqs"

  name_prefix = "sample"

  queues = var.queues

  tags = merge(var.tags, {
    "Service" = "sample"
    "Team"    = "DevOps"
  })
}


module "sns" {
  source = "../../../components/aws/sns"

  name_prefix = "sample"

  topics = var.topics

  tags = merge(var.tags, {
    "Service" = "sample"
    "Team"    = "DevOps"
  })
}

module "rds" {
  source = "../../../components/aws/rds"

  count = var.create_dedicated_rds ? 1 : 0

  name_prefix = "sample"

  name = var.rds_config.name

  db_version = var.rds_config.db_version
  username   = var.rds_config.username
  password   = var.rds_config.password

  subnet_ids         = var.rds_config.subnet_ids
  security_group_ids = var.rds_config.security_group_ids
  instances          = var.rds_config.instances

  tags = merge(var.tags, {
    "Service" = "sample"
    "Team"    = "DevOps"
  })
}
