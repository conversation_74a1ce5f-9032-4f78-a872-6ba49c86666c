variable "queues" {
  type = list(object({
    name = string
  }))
}

variable "tags" {
  type = map(string)
}


variable "topics" {
  type = list(object({
    name = string
    subscriptions = optional(list(object({
      protocol = string
      endpoint = string
    })))
  }))
}

variable "create_dedicated_rds" {
  type    = bool
  default = false
}

variable "rds_config" {
  type = object({
    name               = string
    db_version         = string
    username           = string
    password           = string
    subnet_ids         = list(string)
    vpc_id             = string
    security_group_ids = list(string)
    instances = list(object({
      id    = string
      class = string
      az    = string
    }))
  })

}
