variable "queues" {
  type = list(object({
    name                        = string
    fifo_queue                  = optional(bool, false)
    enable_dlq                  = optional(bool, false)
    content_based_deduplication = optional(bool)
  }))

  default     = []
  description = "List of SQS queues to create"
}

variable "ecr_repositories" {
  type = list(object({
    name                 = string
    image_tag_mutability = optional(string)
  }))
  default     = []
  description = "List of ECR repositories to create"
}

variable "tags" {
  type        = map(string)
  description = "Tags to apply to all resources"
  default = {
    "Team"    = "ReputationManagement"
    "Service" = "ReputationManagement"
  }
}
