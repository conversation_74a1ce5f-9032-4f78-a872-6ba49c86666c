output "queue_urls" {
  description = "Map of queue names to their URLs"
  value       = module.sqs.queue_urls
}

output "queue_arns" {
  description = "Map of queue names to their ARNs"
  value       = module.sqs.queue_arns
}

output "dlq_urls" {
  description = "Map of DLQ names to their URLs"
  value       = module.sqs.dlq_urls
}

output "dlq_arns" {
  description = "Map of DLQ names to their ARNs"
  value       = module.sqs.dlq_arns
}

output "ecr_repository_details" {
  description = "Details of the created ECR repositories."
  value = {
    for k, repo in module.ecr.ecr_repositories_details : k => {
      arn            = repo.arn
      name           = repo.name
      repository_url = repo.repository_url
      registry_id    = repo.registry_id
    }
  }
}
