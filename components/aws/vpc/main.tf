locals {
  aws_vpc_cidr_block = "${var.cidr_base}/16"
}

#trivy:ignore:AVD-AWS-0178
resource "aws_vpc" "main_vpc" {
  cidr_block                           = local.aws_vpc_cidr_block
  enable_dns_hostnames                 = true
  enable_dns_support                   = true
  instance_tenancy                     = "default"
  assign_generated_ipv6_cidr_block     = true
  enable_network_address_usage_metrics = true

  tags = merge(var.tags, {
    "Name" = "${var.name_prefix}-vpc"
  })
}

data "aws_availability_zones" "azs" {}

locals {
  az_count = length(data.aws_availability_zones.azs.names)
  final_subnet_count = coalesce(
    try(min(var.subnet_count, local.az_count), local.az_count),
    local.az_count
  )
}

# Create private subnets for each AZ
resource "aws_subnet" "private" {
  count             = local.final_subnet_count
  vpc_id            = aws_vpc.main_vpc.id
  cidr_block        = cidrsubnet(local.aws_vpc_cidr_block, 4, count.index)
  ipv6_cidr_block   = cidrsubnet(aws_vpc.main_vpc.ipv6_cidr_block, 4, count.index)
  availability_zone = data.aws_availability_zones.azs.names[count.index]

  tags = merge(var.tags, var.pvt_sbnt_tags, {
    "Name" : "${var.name_prefix}-pvt-sbnt-${substr(data.aws_availability_zones.azs.names[count.index], -1, 1)}"
  })
}
# Create public subnets for each AZ
resource "aws_subnet" "public" {
  count             = local.final_subnet_count
  vpc_id            = aws_vpc.main_vpc.id
  cidr_block        = cidrsubnet(aws_vpc.main_vpc.cidr_block, 8, 200 + count.index)
  ipv6_cidr_block   = cidrsubnet(aws_vpc.main_vpc.ipv6_cidr_block, 4, local.final_subnet_count + count.index)
  availability_zone = data.aws_availability_zones.azs.names[count.index]

  tags = merge(var.tags, var.pblc_sbnt_tags, {
    "Name" : "${var.name_prefix}-pblc-sbnt-${substr(data.aws_availability_zones.azs.names[count.index], -1, 1)}"
  })
}

resource "aws_eip" "nat_eip" {
  tags = merge(var.tags, {
    Name = "${var.name_prefix}-nat-eip"
  })
}

resource "aws_nat_gateway" "vpc_ngw" {
  allocation_id     = aws_eip.nat_eip.id
  subnet_id         = aws_subnet.public[0].id
  connectivity_type = "public"

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-ngw"
  })
}

resource "aws_internet_gateway" "vpc_igw" {
  vpc_id = aws_vpc.main_vpc.id

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-igw"
  })
}

resource "aws_egress_only_internet_gateway" "vpc_eigw" {
  vpc_id = aws_vpc.main_vpc.id

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-eigw"
  })
}

resource "aws_route_table" "pblc_rtb" {
  vpc_id = aws_vpc.main_vpc.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.vpc_igw.id
  }

  route {
    ipv6_cidr_block = "::/0"
    gateway_id      = aws_internet_gateway.vpc_igw.id
  }

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-pblc-rtb"
  })
}

resource "aws_route_table" "pvt_rtb" {
  vpc_id = aws_vpc.main_vpc.id

  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = aws_nat_gateway.vpc_ngw.id
  }

  route {
    ipv6_cidr_block        = "::/0"
    egress_only_gateway_id = aws_egress_only_internet_gateway.vpc_eigw.id
  }

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-pvt-rtb"
  })
}

resource "aws_route_table_association" "pvt_rtb_association" {
  route_table_id = aws_route_table.pvt_rtb.id
  count          = local.final_subnet_count
  subnet_id      = aws_subnet.private[count.index].id
}

resource "aws_route_table_association" "pblc_rtb_association" {
  route_table_id = aws_route_table.pblc_rtb.id
  count          = local.final_subnet_count
  subnet_id      = aws_subnet.public[count.index].id
}

locals {
  ipv4_all = "0.0.0.0/0"
  ipv6_all = "::/0"

  default_nacl_egress_rules = [
    { rule_no = 100, cidr_block = local.ipv4_all, protocol = "-1", from_port = 0, to_port = 0, icmp_type = 0, icmp_code = 0 },
    { rule_no = 200, ipv6_cidr_block = local.ipv6_all, protocol = "-1", from_port = 0, to_port = 0, icmp_type = 0, icmp_code = 0 }
  ]

  default_nacl_ingress_rules = [
    # Deny rules
    { rule_no = 100, cidr_block = local.ipv4_all, protocol = "6", from_port = 3389, to_port = 3389, action = "deny" },
    { rule_no = 110, ipv6_cidr_block = local.ipv6_all, protocol = "6", from_port = 3389, to_port = 3389, action = "deny" },

    # HTTP Rules
    { rule_no = 120, cidr_block = local.ipv4_all, protocol = "6", from_port = 80, to_port = 80 },
    { rule_no = 130, ipv6_cidr_block = local.ipv6_all, protocol = "6", from_port = 80, to_port = 80 },

    # HTTPS Rules
    { rule_no = 140, cidr_block = local.ipv4_all, protocol = "6", from_port = 443, to_port = 443 },
    { rule_no = 150, ipv6_cidr_block = local.ipv6_all, protocol = "6", from_port = 443, to_port = 443 },

    # TCP allow ports
    { rule_no = 160, cidr_block = local.ipv4_all, protocol = "6", from_port = 1024, to_port = 65535 },
    { rule_no = 170, ipv6_cidr_block = local.ipv6_all, protocol = "6", from_port = 1024, to_port = 65535 },

    # Allow current VPC traffic
    { rule_no = 180, cidr_block = aws_vpc.main_vpc.cidr_block, protocol = "-1", from_port = 0, to_port = 0 },
    { rule_no = 190, ipv6_cidr_block = aws_vpc.main_vpc.ipv6_cidr_block, protocol = "-1", from_port = 0, to_port = 0 },
  ]
}

resource "aws_default_network_acl" "nacl" {
  default_network_acl_id = aws_vpc.main_vpc.default_network_acl_id
  subnet_ids             = concat(aws_subnet.private[*].id, aws_subnet.public[*].id)

  tags = merge(var.tags, {
    "Name" = "${var.name_prefix}-nacl"
  })

  dynamic "egress" {
    for_each = concat(local.default_nacl_egress_rules, var.nacl_egress_rules)

    content {
      action          = lookup(egress.value, "action", "allow")
      from_port       = egress.value["from_port"]
      icmp_code       = egress.value["icmp_code"]
      icmp_type       = egress.value["icmp_type"]
      cidr_block      = lookup(egress.value, "cidr_block", null)
      ipv6_cidr_block = lookup(egress.value, "ipv6_cidr_block", null)
      protocol        = egress.value["protocol"]
      rule_no         = egress.value["rule_no"]
      to_port         = egress.value["to_port"]
    }
  }

  dynamic "ingress" {
    for_each = concat(local.default_nacl_ingress_rules, var.nacl_ingress_rules)

    content {
      action          = lookup(ingress.value, "action", "allow")
      rule_no         = ingress.value.rule_no
      protocol        = ingress.value.protocol
      from_port       = ingress.value.from_port
      to_port         = ingress.value.to_port
      icmp_type       = 0
      icmp_code       = 0
      cidr_block      = lookup(ingress.value, "cidr_block", null)
      ipv6_cidr_block = lookup(ingress.value, "ipv6_cidr_block", null)
    }
  }
}
