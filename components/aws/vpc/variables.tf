variable "cidr_base" {
  type        = string
  description = "The base CIDR (Eg. **********)"
}

variable "name_prefix" {
  type        = string
  description = "The name prefix to use"
}

variable "subnet_count" {
  type        = number
  description = "The number of subnets to create"
  default     = null
}

variable "tags" {
  type        = map(string)
  description = "A map of tags to add to all resources"
}

variable "pvt_sbnt_tags" {
  type        = map(string)
  default     = {}
  description = "A map of tags to add to private subnets"
}

variable "pblc_sbnt_tags" {
  type        = map(string)
  default     = {}
  description = "A map of tags to add to public subnets"
}

variable "nacl_ingress_rules" {
  type = list(object({
    action          = string
    from_port       = number
    icmp_code       = optional(number, 0)
    icmp_type       = optional(number, 0)
    cidr_block      = optional(string, null)
    ipv6_cidr_block = optional(string, null)
    protocol        = string
    rule_no         = number
    to_port         = number
  }))

  default     = []
  description = "Array of ingress rules to use for NACL"
}

variable "nacl_egress_rules" {
  type = list(object({
    action          = string
    from_port       = number
    icmp_code       = optional(number, 0)
    icmp_type       = optional(number, 0)
    cidr_block      = optional(string, null)
    ipv6_cidr_block = optional(string, null)
    protocol        = string
    rule_no         = number
    to_port         = number
  }))

  default     = []
  description = "Array for egress rules to use for NACL"
}
