## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.9 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.0 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_default_network_acl.nacl](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/default_network_acl) | resource |
| [aws_egress_only_internet_gateway.vpc_eigw](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/egress_only_internet_gateway) | resource |
| [aws_eip.nat_eip](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/eip) | resource |
| [aws_internet_gateway.vpc_igw](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/internet_gateway) | resource |
| [aws_nat_gateway.vpc_ngw](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/nat_gateway) | resource |
| [aws_route_table.pblc_rtb](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route_table) | resource |
| [aws_route_table.pvt_rtb](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route_table) | resource |
| [aws_route_table_association.pblc_rtb_association](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route_table_association) | resource |
| [aws_route_table_association.pvt_rtb_association](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/route_table_association) | resource |
| [aws_subnet.private](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/subnet) | resource |
| [aws_subnet.public](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/subnet) | resource |
| [aws_vpc.main_vpc](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/vpc) | resource |
| [aws_availability_zones.azs](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/availability_zones) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_cidr_base"></a> [cidr\_base](#input\_cidr\_base) | The base CIDR (Eg. **********) | `string` | n/a | yes |
| <a name="input_nacl_egress_rules"></a> [nacl\_egress\_rules](#input\_nacl\_egress\_rules) | Array for egress rules to use for NACL | <pre>list(object({<br/>    action          = string<br/>    from_port       = number<br/>    icmp_code       = optional(number, 0)<br/>    icmp_type       = optional(number, 0)<br/>    cidr_block      = optional(string, null)<br/>    ipv6_cidr_block = optional(string, null)<br/>    protocol        = string<br/>    rule_no         = number<br/>    to_port         = number<br/>  }))</pre> | `[]` | no |
| <a name="input_nacl_ingress_rules"></a> [nacl\_ingress\_rules](#input\_nacl\_ingress\_rules) | Array of ingress rules to use for NACL | <pre>list(object({<br/>    action          = string<br/>    from_port       = number<br/>    icmp_code       = optional(number, 0)<br/>    icmp_type       = optional(number, 0)<br/>    cidr_block      = optional(string, null)<br/>    ipv6_cidr_block = optional(string, null)<br/>    protocol        = string<br/>    rule_no         = number<br/>    to_port         = number<br/>  }))</pre> | `[]` | no |
| <a name="input_name_prefix"></a> [name\_prefix](#input\_name\_prefix) | The name prefix to use | `string` | n/a | yes |
| <a name="input_pblc_sbnt_tags"></a> [pblc\_sbnt\_tags](#input\_pblc\_sbnt\_tags) | A map of tags to add to public subnets | `map(string)` | `{}` | no |
| <a name="input_pvt_sbnt_tags"></a> [pvt\_sbnt\_tags](#input\_pvt\_sbnt\_tags) | A map of tags to add to private subnets | `map(string)` | `{}` | no |
| <a name="input_subnet_count"></a> [subnet\_count](#input\_subnet\_count) | The number of subnets to create | `number` | `null` | no |
| <a name="input_tags"></a> [tags](#input\_tags) | A map of tags to add to all resources | `map(string)` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_private_subnet_ids"></a> [private\_subnet\_ids](#output\_private\_subnet\_ids) | The IDs of the private subnets |
| <a name="output_public_subnet_ids"></a> [public\_subnet\_ids](#output\_public\_subnet\_ids) | The IDs of the public subnets |
| <a name="output_vpc_id"></a> [vpc\_id](#output\_vpc\_id) | The created VPC Identifier |
| <a name="output_vpc_ipv4_cidr"></a> [vpc\_ipv4\_cidr](#output\_vpc\_ipv4\_cidr) | The IPv4 CIDR block |
| <a name="output_vpc_ipv6_cidr"></a> [vpc\_ipv6\_cidr](#output\_vpc\_ipv6\_cidr) | The IPv6 CIDR block |
| <a name="output_vpc_ngw_ipv4"></a> [vpc\_ngw\_ipv4](#output\_vpc\_ngw\_ipv4) | value of the public IPv4 address of the NAT gateway |

## Module Summary

This VPC module provides a standardized approach to creating and configuring AWS Virtual Private Clouds with dual-stack (IPv4 and IPv6) networking. It creates a complete VPC environment with public and private subnets distributed across multiple availability zones, along with the necessary networking components for secure and reliable infrastructure deployment.

## Features

- Creates a VPC with automatic IPv6 CIDR block assignment
- Distributes subnets across all available availability zones in the region
- Creates both public and private subnets with appropriate route tables
- Configures NAT Gateway for private subnet internet access
- Sets up Internet Gateway for public subnet access
- Includes Egress-Only Internet Gateway for IPv6 outbound traffic
- Implements Network ACLs with sensible security defaults
- Blocks common attack vectors through default NACL rules
- Automatically calculates subnet CIDR blocks based on the VPC CIDR range
- Enables DNS hostnames and DNS support in the VPC
- Provides consistent naming and tagging for all resources
- Configurable subnet counts to match specific requirements
- Outputs essential resource IDs for reference in other Terraform configurations

## Usage Examples

### Example 1:

This example creates a simple VPC with public and private subnets in all available AZs:

```hcl
module "vpc" {
  source = "sparrow-tofu-modules/components/aws/vpc"

  name_prefix = "myapp-dev"
  cidr_base   = "10.0.0.0"

  # Use default subnet count (all available AZs)

  tags = {
    Environment = "development"
    Project     = "MyApplication"
    ManagedBy   = "Terraform"
  }
}

### Example 2:

This example demonstrates a more complex setup with custom subnet counts, NACL rules, and specific tagging requirements:

```hcl
module "vpc" {
  source = "sparrow-tofu-modules/components/aws/vpc"

  name_prefix  = "production"
  cidr_base    = "**********"
  subnet_count = 3  # Limit to 3 AZs even if more are available

  # Custom tags for organization and cost allocation
  tags = {
    Environment = "production"
    CostCenter  = "infrastructure"
    Department  = "engineering"
    ManagedBy   = "terraform"
  }

  # Additional tags specifically for private subnets
  pvt_sbnt_tags = {
    Tier            = "private"
    DataClassification = "sensitive"
  }

  # Additional tags specifically for public subnets
  pblc_sbnt_tags = {
    Tier          = "public"
    InternetFacing = "true"
  }

  # Custom NACL rules to restrict traffic
  nacl_ingress_rules = [
    # Allow SSH only from corporate IP range
    {
      rule_no         = 200
      protocol        = "tcp"
      action          = "allow"
      cidr_block      = "***********/24"  # Example corporate IP range
      from_port       = 22
      to_port         = 22
    },
    # Allow specific application port
    {
      rule_no         = 210
      protocol        = "tcp"
      action          = "allow"
      cidr_block      = "0.0.0.0/0"
      from_port       = 8443
      to_port         = 8443
    },
    # Block a specific IP range (e.g., known threat actors)
    {
      rule_no         = 220
      protocol        = "-1"  # All traffic
      action          = "deny"
      cidr_block      = "************/24"
      from_port       = 0
      to_port         = 0
    }
  ]

  # Custom egress rules
  nacl_egress_rules = [
    # Restrict outbound DB connections to specific CIDR
    {
      rule_no         = 200
      protocol        = "tcp"
      action          = "allow"
      cidr_block      = "10.0.0.0/8"
      from_port       = 5432
      to_port         = 5432
    }
  ]
}

# Output CIDR ranges for documentation
output "vpc_cidr" {
  value = module.vpc.vpc_ipv4_cidr
  description = "The IPv4 CIDR range of the VPC"
}

output "vpc_ipv6_cidr" {
  value = module.vpc.vpc_ipv6_cidr
  description = "The IPv6 CIDR range of the VPC"
}

```
