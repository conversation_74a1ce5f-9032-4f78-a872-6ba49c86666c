output "vpc_id" {
  value       = aws_vpc.main_vpc.id
  description = "The created VPC Identifier"
}

output "vpc_ipv4_cidr" {
  value       = aws_vpc.main_vpc.cidr_block
  description = "The IPv4 CIDR block"
}

output "vpc_ipv6_cidr" {
  value       = aws_vpc.main_vpc.ipv6_cidr_block
  description = "The IPv6 CIDR block"
}

output "vpc_ngw_ipv4" {
  value       = aws_nat_gateway.vpc_ngw.public_ip
  description = "value of the public IPv4 address of the NAT gateway"
}

output "private_subnet_ids" {
  value       = aws_subnet.private[*].id
  description = "The IDs of the private subnets"
}

output "public_subnet_ids" {
  value       = aws_subnet.public[*].id
  description = "The IDs of the public subnets"
}
