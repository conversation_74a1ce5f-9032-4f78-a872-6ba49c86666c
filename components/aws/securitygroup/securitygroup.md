## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.9 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.0 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| aws_security_group.this | resource |
| aws_vpc_security_group_egress_rule.this | resource |
| aws_vpc_security_group_ingress_rule.this | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_name_prefix"></a> [name\_prefix](#input\_name\_prefix) | Prefix to be used for all resource names | `string` | n/a | yes |
| <a name="input_security_groups"></a> [security\_groups](#input\_security\_groups) | List of security groups to create | <pre>list(object({<br/>    name        = string<br/>    vpc_id      = string<br/>    description = optional(string)<br/><br/>    ingress_rules = optional(list(object({<br/>      from_port       = number<br/>      to_port         = number<br/>      protocol        = string<br/>      description     = optional(string)<br/>      ipv4_cidr_block = optional(string)<br/>      ipv6_cidr_block = optional(string)<br/>      # prefix_list_ids           = optional(list(string))<br/>      # source_security_group_id = optional(string)<br/>      # self                     = optional(bool)<br/>    })), [])<br/><br/>    egress_rules = optional(list(object({<br/>      from_port       = number<br/>      to_port         = number<br/>      protocol        = string<br/>      description     = optional(string)<br/>      ipv4_cidr_block = optional(string)<br/>      ipv6_cidr_block = optional(string)<br/>      # prefix_list_ids           = optional(list(string))<br/>      # source_security_group_id = optional(string)<br/>      # self                     = optional(bool)<br/>    })), [])<br/>  }))</pre> | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | Tags to be applied to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_security_group_arns"></a> [security\_group\_arns](#output\_security\_group\_arns) | Map of security group names to their ARNs |
| <a name="output_security_group_ids"></a> [security\_group\_ids](#output\_security\_group\_ids) | Map of security group names to their IDs |
| <a name="output_security_group_names"></a> [security\_group\_names](#output\_security\_group\_names) | Map of security group names to their actual names in AWS |

## Module Summary

This SecurityGroup module provides a standardized approach to creating and managing AWS security groups and their associated ingress and egress rules. It uses the latest AWS provider's individual rule resources (`aws_vpc_security_group_ingress_rule` and `aws_vpc_security_group_egress_rule`) rather than inline rules, following AWS best practices for better maintainability and rule management.

## Features

- Creates multiple security groups with a single module call
- Manages ingress and egress rules as separate resources for better visibility and management
- Provides sensible defaults for egress rules (allows all outbound traffic for both IPv4 and IPv6)
- Supports both IPv4 and IPv6 CIDR blocks for rules
- Uses consistent naming conventions with customizable prefix
- Applies standardized tagging to all resources
- Creates security groups with `create_before_destroy` lifecycle policy to prevent dependency issues during updates
- Provides useful outputs with maps of security group names to IDs, ARNs, and AWS names
- Includes descriptive labels for both security groups and their rules

## Usage Examples

### Example 1:

This example creates a simple web application security group with HTTP, HTTPS, and SSH access:

```hcl
module "basic_security_groups" {
  source = "sparrow-tofu-modules/components/aws/securitygroup"

  name_prefix = "webapp-dev"

  security_groups = [
    {
      name        = "web-server"
      description = "Security group for web servers"
      vpc_id      = "vpc-1234567890abcdef0"

      ingress_rules = [
        {
          # HTTP access from anywhere
          from_port       = 80
          to_port         = 80
          protocol        = "tcp"
          description     = "Allow HTTP traffic"
          ipv4_cidr_block = "0.0.0.0/0"
        },
        {
          # HTTPS access from anywhere
          from_port       = 443
          to_port         = 443
          protocol        = "tcp"
          description     = "Allow HTTPS traffic"
          ipv4_cidr_block = "0.0.0.0/0"
        },
        {
          # SSH access from company office IP
          from_port       = 22
          to_port         = 22
          protocol        = "tcp"
          description     = "SSH from office"
          ipv4_cidr_block = "***********/24"  # Example company office IP range
        }
      ]
      # No need to specify egress_rules - module will create default "allow all outbound" rules
    }
  ]

  tags = {
    Environment = "development"
    Project     = "WebApp"
    ManagedBy   = "Terraform"
  }
}

# Output the created security group IDs for use in other resources
output "web_server_sg_id" {
  value = module.basic_security_groups.security_group_ids["web-server"]
}
```

### Example 2:

This example demonstrates a more complex setup with multiple security groups, custom egress rules, and different protocols:

```hcl
module "advanced_security_groups" {
  source = "sparrow-tofu-modules/components/aws/securitygroup"

  name_prefix = "app-prod"

  security_groups = [
    {
      name        = "app-servers"
      description = "Security group for application servers"
      vpc_id      = "vpc-1234567890abcdef0"

      ingress_rules = [
        {
          # Allow application traffic from load balancer
          from_port       = 8080
          to_port         = 8080
          protocol        = "tcp"
          description     = "Application port from load balancer"
          ipv4_cidr_block = "10.0.0.0/16"  # VPC CIDR block
        },
        {
          # Allow secure admin access with custom port
          from_port       = 8443
          to_port         = 8443
          protocol        = "tcp"
          description     = "Admin interface"
          ipv4_cidr_block = "*********/24"  # Admin subnet
        },
        {
          # Allow health checks
          from_port       = 8081
          to_port         = 8081
          protocol        = "tcp"
          description     = "Health check endpoint"
          ipv4_cidr_block = "10.0.0.0/16"
        },
        {
          # Allow SSH from bastion hosts
          from_port       = 22
          to_port         = 22
          protocol        = "tcp"
          description     = "SSH from bastion hosts"
          ipv4_cidr_block = "********/28"  # Bastion host subnet
        }
      ],

      # Custom egress rules - more restrictive than defaults
      egress_rules = [
        {
          # Allow outbound to database
          from_port       = 5432
          to_port         = 5432
          protocol        = "tcp"
          description     = "PostgreSQL access"
          ipv4_cidr_block = "*********/24"  # Database subnet
        },
        {
          # Allow outbound to Redis
          from_port       = 6379
          to_port         = 6379
          protocol        = "tcp"
          description     = "Redis access"
          ipv4_cidr_block = "*********/24"  # Redis subnet
        },
        {
          # Allow DNS queries
          from_port       = 53
          to_port         = 53
          protocol        = "udp"
          description     = "DNS queries"
          ipv4_cidr_block = "0.0.0.0/0"
        },
        {
          # Allow HTTPS for package updates and API calls
          from_port       = 443
          to_port         = 443
          protocol        = "tcp"
          description     = "HTTPS outbound"
          ipv4_cidr_block = "0.0.0.0/0"
        }
      ]
    },

    {
      name        = "db-servers"
      description = "Security group for database servers"
      vpc_id      = "vpc-1234567890abcdef0"

      ingress_rules = [
        {
          # Allow PostgreSQL from application servers only
          from_port       = 5432
          to_port         = 5432
          protocol        = "tcp"
          description     = "PostgreSQL from app servers"
          ipv4_cidr_block = "10.0.0.0/24"  # App servers subnet
        }
      ],

      egress_rules = [
        {
          # Restrict outbound to only necessary services
          from_port       = 443
          to_port         = 443
          protocol        = "tcp"
          description     = "HTTPS for updates"
          ipv4_cidr_block = "0.0.0.0/0"
        }
      ]
    },

    {
      name        = "redis-cache"
      description = "Security group for Redis cache servers"
      vpc_id      = "vpc-1234567890abcdef0"

      ingress_rules = [
        {
          # Allow Redis access from application servers
          from_port       = 6379
          to_port         = 6379
          protocol        = "tcp"
          description     = "Redis from app servers"
          ipv4_cidr_block = "10.0.0.0/24"
        }
      ]
    }
  ]

  tags = {
    Environment = "production"
    Application = "MainApp"
    ManagedBy   = "Terraform"
    Team        = "Infrastructure"
    CostCenter  = "IT-123"
  }
}

```
