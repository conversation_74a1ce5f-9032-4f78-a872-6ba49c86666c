locals {
  # Convert security groups list to map for easier lookup
  security_group_map = {
    for idx, sg in var.security_groups : sg.name => sg
  }

  default_egress_rules = [
    {
      from_port       = "-1"
      to_port         = "-1"
      protocol        = "-1"
      ipv4_cidr_block = "0.0.0.0/0"
      description     = "Allow all outbound ipv4 traffic"
    },
    {
      from_port       = "-1"
      to_port         = "-1"
      protocol        = "-1"
      ipv6_cidr_block = "::/0"
      description     = "Allow all outbound ipv6 traffic"
    }
  ]

  # Flatten and normalize ingress rules
  ingress_rules = flatten([
    for sg_name, sg in local.security_group_map : [
      for idx, rule in try(sg.ingress_rules, []) : merge(rule, {
        security_group_name = sg_name
        rule_key            = "${sg_name}-ingress-${idx}"
      })
    ]
  ])

  # Flatten and normalize egress rules with defaults if none specified
  egress_rules = flatten([
    for sg_name, sg in local.security_group_map : [
      for idx, rule in(length(try(sg.egress_rules, [])) > 0 ? sg.egress_rules : local.default_egress_rules) : merge(rule, {
        security_group_name = sg_name
        rule_key            = "${sg_name}-egress-${idx}"
      })
    ]
  ])
}

resource "aws_security_group" "this" {
  for_each = local.security_group_map

  name        = "${var.name_prefix}-${each.value.name}-sg"
  description = try(each.value.description, "Security group for ${each.value.name}")
  vpc_id      = each.value.vpc_id

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${each.value.name}-sg"
  })

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_vpc_security_group_ingress_rule" "this" {
  for_each = {
    for rule in local.ingress_rules : rule.rule_key => rule
  }

  from_port         = each.value.from_port
  to_port           = each.value.to_port
  ip_protocol       = each.value.protocol
  description       = try(each.value.description, "Ingress rule for ${each.value.security_group_name}")
  security_group_id = aws_security_group.this[each.value.security_group_name].id

  cidr_ipv4      = try(each.value.ipv4_cidr_block, null)
  cidr_ipv6      = try(each.value.ipv6_cidr_block, null)
  prefix_list_id = try(each.value.prefix_list_ids, null)
  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${each.value.rule_key}"
  })

  # Referenced security group ID will need to be handled separately
  # as aws_vpc_security_group_ingress_rule only supports a single reference
  # referenced_security_group_id = try(each.value.source_security_group_id, null)
}

resource "aws_vpc_security_group_egress_rule" "this" {
  for_each = {
    for rule in local.egress_rules : rule.rule_key => rule
  }

  from_port         = each.value.from_port
  to_port           = each.value.to_port
  ip_protocol       = each.value.protocol
  description       = try(each.value.description, "Egress rule for ${each.value.security_group_name}")
  security_group_id = aws_security_group.this[each.value.security_group_name].id

  cidr_ipv4      = try(each.value.ipv4_cidr_block, null)
  cidr_ipv6      = try(each.value.ipv6_cidr_block, null)
  prefix_list_id = try(each.value.prefix_list_ids, null)

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${each.value.rule_key}"
  })

  # Referenced security group ID will need to be handled separately
  # as aws_vpc_security_group_egress_rule only supports a single reference
  # referenced_security_group_id = try(each.value.source_security_group_id, null)
}
