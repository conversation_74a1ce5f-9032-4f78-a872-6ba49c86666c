variable "name_prefix" {
  type        = string
  description = "Prefix to be used for all resource names"
}

variable "tags" {
  type        = map(string)
  description = "Tags to be applied to all resources"
}

# Commented out certain options, as we have not used them in our organization.
# We will uncomment them if we need to use them.
variable "security_groups" {
  type = list(object({
    name        = string
    vpc_id      = string
    description = optional(string)

    ingress_rules = optional(list(object({
      from_port       = number
      to_port         = number
      protocol        = string
      description     = optional(string)
      ipv4_cidr_block = optional(string)
      ipv6_cidr_block = optional(string)
      # prefix_list_ids           = optional(list(string))
      # source_security_group_id = optional(string)
      # self                     = optional(bool)
    })), [])

    egress_rules = optional(list(object({
      from_port       = number
      to_port         = number
      protocol        = string
      description     = optional(string)
      ipv4_cidr_block = optional(string)
      ipv6_cidr_block = optional(string)
      # prefix_list_ids           = optional(list(string))
      # source_security_group_id = optional(string)
      # self                     = optional(bool)
    })), [])
  }))
  description = "List of security groups to create"

  validation {
    condition     = length(var.security_groups) == length(distinct([for group in var.security_groups : group.name]))
    error_message = "Security group names must be unique within the list."
  }
}
