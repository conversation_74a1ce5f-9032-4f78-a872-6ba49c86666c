data "aws_region" "current" {}
data "aws_kms_key" "secretsmanager" {
  key_id = "alias/aws/secretsmanager"
}

locals {
  # DB Default settings
  db_default_settings = {
    engine                     = "aurora-postgresql"
    engine_family              = "POSTGRESQL"
    backup_retention_period    = 7
    encrypt_storage            = true
    kms_key_id                 = null # "alias/aws/rds"
    allow_delete               = false
    skip_final_snapshot        = false
    copy_tags_to_snapshot      = true
    apply_immediately          = false
    auto_minor_version_upgrade = true
    network_type               = "DUAL"

    monitoring_interval   = 60
    monitoring_policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonRDSEnhancedMonitoringRole"
  }

  # Variables for DB Class validation
  allowed_instance_classes = [
    "db.t4g.medium",
    "db.t4g.large",
    "db.r7g.large",
    "db.r7g.xlarge",
    "db.r7g.2xlarge",
    "db.r6g.large",
    "db.r6g.xlarge",
    "db.r6g.2xlarge",
    "db.r6gd.large",
    "db.r6gd.xlarge",
    "db.r6gd.2xlarge",
  ]

  invalid_instance_classes = [
    for instance in var.instances :
    instance.class if !contains(local.allowed_instance_classes, instance.class)
  ]

  create_cluster = length(local.invalid_instance_classes) == 0

  # Variables for Parameter Group family
  parameter_group_family = "${local.db_default_settings.engine}${split(".", var.db_version)[0]}"

  # Maintenance window calculation
  region_to_utc_offset = {
    "us-east-1"      = -5 # N. Virginia (EST)
    "us-east-2"      = -5 # Ohio (EST)
    "eu-west-2"      = 0  # London (GMT)
    "ap-south-1"     = 5  # Mumbai (IST) - using 5 instead of 5.5 for simplicity
    "ap-southeast-2" = 10 # Sydney (AEST)
    "me-central-1"   = 3  # Bahrain (AST)
    "default"        = 0
  }

  current_utc_offset            = lookup(local.region_to_utc_offset, data.aws_region.current.name, local.region_to_utc_offset["default"])
  maintenance_start_hour        = (22 - local.current_utc_offset) % 24
  maintenance_end_hour          = (0 - local.current_utc_offset) % 24
  maintenance_start_day         = "sat"
  maintenance_end_day           = local.maintenance_start_hour > local.maintenance_end_hour ? "sun" : "sat"
  calculated_maintenance_window = "${local.maintenance_start_day}:${format("%02d", local.maintenance_start_hour)}:00-${local.maintenance_end_day}:${format("%02d", local.maintenance_end_hour)}:00"

  # Calculate backup window to be 4 hours before maintenance window
  backup_start_hour        = (local.maintenance_start_hour - 4 + 24) % 24
  backup_end_hour          = (local.maintenance_start_hour + 24) % 24
  calculated_backup_window = "${format("%02d", local.backup_start_hour)}:00-${format("%02d", local.backup_end_hour)}:00"

  # Use provided windows if specified, otherwise use calculated values
  maintenance_window = var.preferred_maintenance_window != null ? var.preferred_maintenance_window : local.calculated_maintenance_window
  backup_window      = var.preferred_backup_window != null ? var.preferred_backup_window : local.calculated_backup_window

  unique_instance_classes = toset([for instance in var.instances : instance.class])
}

# Fail if any instance class is not in the allowed list
resource "null_resource" "validate_instance_classes" {
  count = length(local.invalid_instance_classes) > 0 ? 1 : 0

  lifecycle {
    precondition {
      condition     = length(local.invalid_instance_classes) == 0
      error_message = "The following instance classes are not allowed: ${join(", ", local.invalid_instance_classes)}. Allowed instance classes are: ${join(", ", local.allowed_instance_classes)}"
    }
  }
}

# Create DB subnet group
resource "aws_db_subnet_group" "this" {
  count = local.create_cluster ? 1 : 0

  name        = "${var.name_prefix}-${var.name}-subnet-group"
  subnet_ids  = var.subnet_ids
  description = "Subnet group for ${var.name_prefix}-${var.name}"

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${var.name}-subnet-group"
  })
}

# Create parameter groups if enabled
resource "aws_rds_cluster_parameter_group" "this" {
  count = length(var.cluster_parameters) > 0 && local.create_cluster ? 1 : 0

  name        = "${var.name_prefix}-${var.name}-cluster-pg"
  family      = local.parameter_group_family
  description = "Cluster parameter group for ${var.name_prefix}-${var.name}"

  dynamic "parameter" {
    for_each = var.cluster_parameters
    content {
      name  = parameter.value.name
      value = parameter.value.value
    }
  }

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${var.name}-cluster-pg"
  })
}

resource "aws_db_parameter_group" "this" {
  count = length(var.instance_parameters) > 0 && local.create_cluster ? 1 : 0

  name        = "${var.name_prefix}-${var.name}-instance-pg"
  family      = local.parameter_group_family
  description = "Instance parameter group for ${var.name_prefix}-${var.name}"

  dynamic "parameter" {
    for_each = var.instance_parameters
    content {
      name  = parameter.value.name
      value = parameter.value.value
    }
  }

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${var.name}-instance-pg"
  })
}

module "monitoring_role" {
  source = "../iam"

  name_prefix = "${var.name_prefix}-${var.name}"
  tags        = var.tags

  roles = [
    {
      name                = "rds-monitoring"
      trust_relationship  = file("${path.module}/files/iam/trust/monitoring.json")
      managed_policy_arns = [local.db_default_settings.monitoring_policy_arn]
    }
  ]
}

# Seperating into two separate module calls to avoid circular dependency
module "secrets_role" {
  source = "../iam"

  name_prefix = "${var.name_prefix}-${var.name}"
  tags        = var.tags

  roles = [
    {
      name               = "rds-secrets-access"
      trust_relationship = file("${path.module}/files/iam/trust/secretsmanager.json")
      policies = [
        {
          name = "rds-secrets-access"
          policy = templatefile("${path.module}/files/iam/policy/secretaccess.json", {
            secret_arn     = module.secretsmanager.secret_arns["rds-password"]
            kms_key_id     = data.aws_kms_key.secretsmanager.arn
            current_region = data.aws_region.current.name
          })
        }
      ]
    }
  ]
}

# Update the Aurora cluster with secure defaults
resource "aws_rds_cluster" "this" {
  count = local.create_cluster ? 1 : 0

  cluster_identifier        = "${var.name_prefix}-${var.name}"
  engine                    = local.db_default_settings.engine
  engine_version            = var.db_version
  master_username           = var.username
  master_password           = var.password
  skip_final_snapshot       = local.db_default_settings.skip_final_snapshot
  final_snapshot_identifier = "${var.name_prefix}-${var.name}-final-snapshot"

  db_subnet_group_name = aws_db_subnet_group.this[0].name
  network_type         = local.db_default_settings.network_type

  # Security settings
  vpc_security_group_ids = var.security_group_ids
  storage_encrypted      = local.db_default_settings.encrypt_storage
  kms_key_id             = var.kms_key_id != null ? var.kms_key_id : local.db_default_settings.kms_key_id

  deletion_protection   = !local.db_default_settings.allow_delete
  copy_tags_to_snapshot = local.db_default_settings.copy_tags_to_snapshot
  # apply_immediately      = local.db_default_settings.apply_immediately

  # Optional parameter group
  db_cluster_parameter_group_name = length(var.cluster_parameters) > 0 ? aws_rds_cluster_parameter_group.this[0].name : null

  backup_retention_period = local.db_default_settings.backup_retention_period

  preferred_backup_window      = local.backup_window
  preferred_maintenance_window = local.maintenance_window

  # enabled_cloudwatch_logs_exports = ["postgresql"]
  performance_insights_enabled          = true
  performance_insights_retention_period = 7
  monitoring_interval                   = local.db_default_settings.monitoring_interval
  monitoring_role_arn                   = module.monitoring_role.roles["rds-monitoring"].arn

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${var.name}"
  })
}

# Update Aurora instances with secure defaults
resource "aws_rds_cluster_instance" "this" {
  for_each = {
    for idx, instance in var.instances : "${var.name_prefix}-${var.name}-${instance.id}" => instance
    if contains(local.allowed_instance_classes, instance.class) && local.create_cluster
  }

  identifier         = "${var.name_prefix}-${var.name}-${each.value.id}"
  cluster_identifier = aws_rds_cluster.this[0].id
  instance_class     = each.value.class
  engine             = aws_rds_cluster.this[0].engine
  engine_version     = aws_rds_cluster.this[0].engine_version

  availability_zone     = each.value.az
  copy_tags_to_snapshot = local.db_default_settings.copy_tags_to_snapshot
  db_subnet_group_name  = aws_db_subnet_group.this[0].name

  # Optional parameter group
  db_parameter_group_name = length(var.instance_parameters) > 0 ? aws_db_parameter_group.this[0].name : null

  performance_insights_enabled          = true
  performance_insights_retention_period = 7
  monitoring_interval                   = local.db_default_settings.monitoring_interval
  monitoring_role_arn                   = module.monitoring_role.roles["rds-monitoring"].arn

  tags = merge(var.tags, {
    Name = each.key
  })
}

module "secretsmanager" {
  source = "../secretsmanager"

  name_prefix = "${var.name_prefix}-${var.name}"
  tags        = var.tags

  secrets = [
    {
      name        = "rds-password"
      description = "Password for the RDS Aurora cluster - ${var.name_prefix}-${var.name}"
      secret_string = jsonencode({
        username            = var.username
        password            = var.password
        engine              = local.db_default_settings.engine
        engine_version      = var.db_version
        host                = aws_rds_cluster.this[0].endpoint
        port                = aws_rds_cluster.this[0].port
        dbClusterIdentifier = aws_rds_cluster.this[0].id
      })
    }
  ]
}

locals {
  rds_proxy_setting = {
    debug_logging                = false
    idle_client_timeout          = 1800
    require_tls                  = true
    auth_scheme                  = "SECRETS"
    iam_auth                     = "DISABLED"
    connection_borrow_timeout    = 120
    max_connections_percent      = 100
    max_idle_connections_percent = 50
  }
}

resource "aws_db_proxy" "this" {
  count = var.rds_proxy_config != null && local.create_cluster ? 1 : 0

  name                = "${var.name_prefix}-${var.name}-proxy"
  debug_logging       = try(var.rds_proxy_config.debug_logging, local.rds_proxy_setting.debug_logging)
  engine_family       = local.db_default_settings.engine_family
  idle_client_timeout = try(var.rds_proxy_config.idle_client_timeout, local.rds_proxy_setting.idle_client_timeout)

  # TLS SHOULD BE ENABLED BY DEFAULT, BUT THIS IS CAUSING ISSUES CONNECTING from Postico.
  # require_tls = try(var.rds_proxy_config.require_tls, local.rds_proxy_setting.require_tls)

  role_arn               = module.secrets_role.roles["rds-secrets-access"].arn
  vpc_security_group_ids = var.security_group_ids
  vpc_subnet_ids         = var.subnet_ids


  auth {
    auth_scheme = local.rds_proxy_setting.auth_scheme
    iam_auth    = local.rds_proxy_setting.iam_auth
    secret_arn  = module.secretsmanager.secret_arns["rds-password"]
    description = "Proxy authentication for ${var.name_prefix}-${var.name}"
  }

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${var.name}-proxy"
  })
}

resource "aws_db_proxy_default_target_group" "this" {
  count = var.rds_proxy_config != null && local.create_cluster ? 1 : 0

  db_proxy_name = aws_db_proxy.this[0].name

  connection_pool_config {
    connection_borrow_timeout    = local.rds_proxy_setting.connection_borrow_timeout
    max_connections_percent      = local.rds_proxy_setting.max_connections_percent
    max_idle_connections_percent = local.rds_proxy_setting.max_idle_connections_percent
  }
}

resource "aws_db_proxy_target" "this" {
  count = var.rds_proxy_config != null && local.create_cluster ? 1 : 0

  db_proxy_name         = aws_db_proxy.this[0].name
  target_group_name     = aws_db_proxy_default_target_group.this[0].name
  db_cluster_identifier = aws_rds_cluster.this[0].id
}

resource "aws_db_proxy_endpoint" "reader" {
  count = var.rds_proxy_config != null && local.create_cluster ? 1 : 0

  db_proxy_name          = aws_db_proxy.this[0].name
  db_proxy_endpoint_name = "${var.name_prefix}-${var.name}-ro"
  vpc_subnet_ids         = var.subnet_ids
  vpc_security_group_ids = var.security_group_ids
  target_role            = "READ_ONLY"

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${var.name}-ro"
  })
}

data "external" "instance_specs" {
  for_each = local.create_cluster ? { for instance in local.unique_instance_classes : instance => instance } : {}

  program = ["bash", "-c", <<-EOT
    aws ec2 describe-instance-types \
      --instance-types ${replace(each.key, "db.", "")} \
      --query '{
        vcpu: to_string(InstanceTypes[0].VCpuInfo.DefaultVCpus),
        memory: to_string(InstanceTypes[0].MemoryInfo.SizeInMiB)
      }' \
      --output json
    EOT
  ]
}

locals {
  instance_specs = {
    for class, instance in data.external.instance_specs :
    class => {
      vcpu   = tonumber(instance.result.vcpu)
      memory = tonumber(instance.result.memory) # Keep as MiB for comparison
    }
  }

  default_alarms = {
    cpu_utilization = {
      metric_name = "CPUUtilization"
      statistic   = "Average"
      comparison  = "GreaterThanThreshold"
      description = "CPU utilization is too high"
      threshold   = 80
    }
    read_iops = {
      metric_name = "ReadIOPS"
      statistic   = "Maximum"
      comparison  = "GreaterThanOrEqualToThreshold"
      description = "Read IOPS is too high"
      threshold   = 90000
    }
    write_iops = {
      metric_name = "WriteIOPS"
      statistic   = "Maximum"
      comparison  = "GreaterThanOrEqualToThreshold"
      description = "Write IOPS is too high"
      threshold   = 20000
    }
    read_latency = {
      metric_name = "ReadLatency"
      statistic   = "Maximum"
      comparison  = "GreaterThanOrEqualToThreshold"
      description = "Read latency is too high"
      threshold   = 0.250
    }
    write_latency = {
      metric_name = "WriteLatency"
      statistic   = "Maximum"
      comparison  = "GreaterThanOrEqualToThreshold"
      description = "Write latency is too high"
      threshold   = 0.250
    }
    disk_queue_depth = {
      metric_name = "DiskQueueDepth"
      statistic   = "Maximum"
      comparison  = "GreaterThanOrEqualToThreshold"
      description = "Disk queue depth is too high"
      threshold   = 20
    }
    aurora_replica_lag = {
      metric_name = "AuroraReplicaLag"
      statistic   = "Maximum"
      comparison  = "GreaterThanOrEqualToThreshold"
      description = "Aurora replica lag is too high"
      threshold   = 200
    }
  }

  instance_memory_values = [for specs in local.instance_specs : specs.memory]
  max_memory             = length(local.instance_memory_values) > 0 ? max(local.instance_memory_values...) * 1024 * 1024 : 0

  # DB Max Connections for Aurora PyostgreSQL LEAST(DBInstanceClassMemory/9531392,5000)
  max_connections           = min(local.max_memory / 9531392, 5000)
  max_connections_threshold = local.max_connections * 0.4 # 40% of max connections

  # Freeable memory threshold - 50% of max memory (critical level)
  memory_threshold = local.max_memory * 0.5 # 50% of max memory

  specific_alarms = {
    freeable_memory = {
      metric_name = "FreeableMemory"
      statistic   = "Average"
      comparison  = "LessThanThreshold"
      description = "Freeable memory is too low"
      threshold   = local.memory_threshold
    }
    db_connections = {
      metric_name = "DatabaseConnections"
      statistic   = "Maximum"
      comparison  = "GreaterThanOrEqualToThreshold"
      description = "Database connections count is too high"
      threshold   = local.max_connections_threshold
    }
  }
}

module "sns_topic" {
  source = "../sns"

  count = local.create_cluster && var.alarms_url != null ? 1 : 0

  name_prefix = "${var.name_prefix}-${var.name}"
  tags        = var.tags

  topics = [
    {
      name = "rds-alarms"
      subscriptions = [
        {
          protocol = "https"
          endpoint = var.alarms_url
        }
      ]
    }
  ]
}

module "cloudwatch_alarms" {
  source = "../cloudwatch_alarms"

  name_prefix = "${var.name_prefix}-${var.name}"
  tags        = var.tags

  alarms = [
    for alarm in merge(local.default_alarms, local.specific_alarms) : {
      name                = alarm.metric_name
      description         = alarm.description
      metric_name         = alarm.metric_name
      resource            = "rds"
      resource_id         = aws_rds_cluster.this[0].id
      threshold           = alarm.threshold
      comparison_operator = alarm.comparison
      statistic           = alarm.statistic
      alarm_actions       = [try(module.sns_topic[0].topic_arns["rds-alarms"], var.default_sns_topic_arn)]
    }
  ]
}
