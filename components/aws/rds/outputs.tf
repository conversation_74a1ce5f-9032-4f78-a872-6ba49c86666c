output "cluster_id" {
  description = "The ID of the RDS Aurora cluster"
  value       = try(aws_rds_cluster.this[0].id, null)
}

output "cluster_writer_endpoint" {
  description = "The writer endpoint for the RDS Aurora cluster"
  value       = try(aws_rds_cluster.this[0].endpoint, null)
}

output "cluster_reader_endpoint" {
  description = "The reader endpoint for the RDS Aurora cluster"
  value       = try(aws_rds_cluster.this[0].reader_endpoint, null)
}

output "instance_ids" {
  description = "Map of instance identifiers to their IDs"
  value       = { for k, v in aws_rds_cluster_instance.this : k => v.id }
}

output "instance_endpoints" {
  description = "Map of instance identifiers to their endpoints"
  value       = { for k, v in aws_rds_cluster_instance.this : k => v.endpoint }
}

output "proxy_endpoint" {
  description = "The endpoint for the RDS Proxy"
  value       = try(aws_db_proxy.this[0].endpoint, null)
}

output "proxy_endpoint_reader" {
  description = "The endpoint for the RDS Proxy reader"
  value       = try(aws_db_proxy_endpoint.reader[0].endpoint, null)
}
