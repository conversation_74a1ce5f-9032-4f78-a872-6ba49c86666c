variable "name_prefix" {
  type        = string
  description = "Prefix to be used for all resource names"
}

variable "name" {
  type        = string
  description = "Name of the Aurora cluster"
}

variable "db_version" {
  type        = string
  description = "Version of Aurora PostgreSQL to use"
}

variable "username" {
  type        = string
  description = "Master username for the database"
  default     = "root"
}

variable "password" {
  type        = string
  description = "Master password for the database"
  sensitive   = true
}

variable "security_group_ids" {
  type        = list(string)
  description = "List of VPC security group IDs to associate with the cluster"
}

variable "kms_key_id" {
  type        = string
  description = "The ARN for the KMS encryption key. If not specified, the default RDS KMS key is used"
  default     = null
}

variable "cluster_parameters" {
  type = list(object({
    name  = string
    value = string
  }))
  description = "A list of DB parameters to apply to the DB cluster parameter group"
  default     = []
}

variable "instance_parameters" {
  type = list(object({
    name  = string
    value = string
  }))
  description = "A list of DB parameters to apply to the DB instance parameter group"
  default     = []
}

variable "subnet_ids" {
  type        = list(string)
  description = "List of subnet IDs for the DB subnet group"
}

variable "preferred_maintenance_window" {
  type        = string
  description = "Weekly time range during which maintenance can occur (in UTC)"
  default     = null
}

variable "preferred_backup_window" {
  type        = string
  description = "Daily time range during which backups happen (in UTC)"
  default     = null
}

variable "instances" {
  type = list(object({
    id    = string # Getting the ID from the user, since there is no way to randomize it in the state key.
    class = string
    az    = optional(string)
  }))
  description = "List of DB instances to create in the cluster - the larger instance  will be the writer"

  validation {
    condition     = length(var.instances) == length(distinct([for instance in var.instances : instance.id]))
    error_message = "The instance IDs must be unique across all instances in the cluster."
  }

  validation {
    condition     = length(var.instances) > 0
    error_message = "At least one instance must be provided."
  }
}

variable "tags" {
  type        = map(string)
  description = "Additional tags for all resources"
}

variable "rds_proxy_config" {
  type = object({
    enabled             = bool
    debug_logging       = optional(bool)
    idle_client_timeout = optional(number)
  })

  description = "Configuration for the RDS proxy"
  default     = null
}

variable "default_sns_topic_arn" {
  type        = string
  description = "ARN of an existing SNS topic to use for alarm notifications"
  default     = null
}

variable "alarms_url" {
  type        = string
  description = "HTTPS webhook URL for alarm notifications"
  default     = null
}
