## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.9 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.0 |
| <a name="requirement_external"></a> [external](#requirement\_external) | ~> 2.0 |
| <a name="requirement_null"></a> [null](#requirement\_null) | ~> 3.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.0 |
| <a name="provider_external"></a> [external](#provider\_external) | ~> 2.0 |
| <a name="provider_null"></a> [null](#provider\_null) | ~> 3.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_cloudwatch_alarms"></a> [cloudwatch\_alarms](#module\_cloudwatch\_alarms) | ../cloudwatch_alarms | n/a |
| <a name="module_monitoring_role"></a> [monitoring\_role](#module\_monitoring\_role) | ../iam | n/a |
| <a name="module_secrets_role"></a> [secrets\_role](#module\_secrets\_role) | ../iam | n/a |
| <a name="module_secretsmanager"></a> [secretsmanager](#module\_secretsmanager) | ../secretsmanager | n/a |
| <a name="module_sns_topic"></a> [sns\_topic](#module\_sns\_topic) | ../sns | n/a |

## Resources

| Name | Type |
|------|------|
| [aws_db_parameter_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/db_parameter_group) | resource |
| [aws_db_proxy.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/db_proxy) | resource |
| [aws_db_proxy_default_target_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/db_proxy_default_target_group) | resource |
| [aws_db_proxy_endpoint.reader](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/db_proxy_endpoint) | resource |
| [aws_db_proxy_target.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/db_proxy_target) | resource |
| [aws_db_subnet_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/db_subnet_group) | resource |
| [aws_rds_cluster.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/rds_cluster) | resource |
| [aws_rds_cluster_instance.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/rds_cluster_instance) | resource |
| [aws_rds_cluster_parameter_group.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/rds_cluster_parameter_group) | resource |
| [null_resource.validate_instance_classes](https://registry.terraform.io/providers/hashicorp/null/latest/docs/resources/resource) | resource |
| [aws_availability_zones.azs](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/availability_zones) | data source |
| [aws_kms_key.secretsmanager](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/kms_key) | data source |
| [aws_region.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/region) | data source |
| [external_external.instance_specs](https://registry.terraform.io/providers/hashicorp/external/latest/docs/data-sources/external) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_alarms_url"></a> [alarms\_url](#input\_alarms\_url) | HTTPS webhook URL for alarm notifications | `string` | `null` | no |
| <a name="input_cluster_parameters"></a> [cluster\_parameters](#input\_cluster\_parameters) | A list of DB parameters to apply to the DB cluster parameter group | <pre>list(object({<br/>    name  = string<br/>    value = string<br/>  }))</pre> | `[]` | no |
| <a name="input_db_version"></a> [db\_version](#input\_db\_version) | Version of Aurora PostgreSQL to use | `string` | n/a | yes |
| <a name="input_default_sns_topic_arn"></a> [default\_sns\_topic\_arn](#input\_default\_sns\_topic\_arn) | ARN of an existing SNS topic to use for alarm notifications | `string` | `null` | no |
| <a name="input_instance_parameters"></a> [instance\_parameters](#input\_instance\_parameters) | A list of DB parameters to apply to the DB instance parameter group | <pre>list(object({<br/>    name  = string<br/>    value = string<br/>  }))</pre> | `[]` | no |
| <a name="input_instances"></a> [instances](#input\_instances) | List of DB instances to create in the cluster - the larger instance  will be the writer | <pre>list(object({<br/>    id    = string # Getting the ID from the user, since there is no way to randomize it in the state key.<br/>    class = string<br/>    az    = optional(string)<br/>  }))</pre> | `[]` | no |
| <a name="input_kms_key_id"></a> [kms\_key\_id](#input\_kms\_key\_id) | The ARN for the KMS encryption key. If not specified, the default RDS KMS key is used | `string` | `null` | no |
| <a name="input_name"></a> [name](#input\_name) | Name of the Aurora cluster | `string` | n/a | yes |
| <a name="input_name_prefix"></a> [name\_prefix](#input\_name\_prefix) | Prefix to be used for all resource names | `string` | n/a | yes |
| <a name="input_password"></a> [password](#input\_password) | Master password for the database | `string` | n/a | yes |
| <a name="input_preferred_backup_window"></a> [preferred\_backup\_window](#input\_preferred\_backup\_window) | Daily time range during which backups happen (in UTC) | `string` | `null` | no |
| <a name="input_preferred_maintenance_window"></a> [preferred\_maintenance\_window](#input\_preferred\_maintenance\_window) | Weekly time range during which maintenance can occur (in UTC) | `string` | `null` | no |
| <a name="input_rds_proxy_config"></a> [rds\_proxy\_config](#input\_rds\_proxy\_config) | Configuration for the RDS proxy | <pre>object({<br/>    enabled             = bool<br/>    debug_logging       = optional(bool)<br/>    idle_client_timeout = optional(number)<br/>  })</pre> | `null` | no |
| <a name="input_security_group_ids"></a> [security\_group\_ids](#input\_security\_group\_ids) | List of VPC security group IDs to associate with the cluster | `list(string)` | n/a | yes |
| <a name="input_subnet_ids"></a> [subnet\_ids](#input\_subnet\_ids) | List of subnet IDs for the DB subnet group | `list(string)` | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | Additional tags for all resources | `map(string)` | `{}` | no |
| <a name="input_username"></a> [username](#input\_username) | Master username for the database | `string` | `"root"` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_cluster_id"></a> [cluster\_id](#output\_cluster\_id) | The ID of the RDS Aurora cluster |
| <a name="output_cluster_reader_endpoint"></a> [cluster\_reader\_endpoint](#output\_cluster\_reader\_endpoint) | The reader endpoint for the RDS Aurora cluster |
| <a name="output_cluster_writer_endpoint"></a> [cluster\_writer\_endpoint](#output\_cluster\_writer\_endpoint) | The writer endpoint for the RDS Aurora cluster |
| <a name="output_instance_endpoints"></a> [instance\_endpoints](#output\_instance\_endpoints) | Map of instance identifiers to their endpoints |
| <a name="output_instance_ids"></a> [instance\_ids](#output\_instance\_ids) | Map of instance identifiers to their IDs |
| <a name="output_proxy_endpoint"></a> [proxy\_endpoint](#output\_proxy\_endpoint) | The endpoint for the RDS Proxy |
| <a name="output_proxy_endpoint_reader"></a> [proxy\_endpoint\_reader](#output\_proxy\_endpoint\_reader) | The endpoint for the RDS Proxy reader |

## Module Summary

This AWS RDS module provides a comprehensive solution for deploying and managing Aurora PostgreSQL clusters with a focus on security, performance, and operational best practices. It creates a fully configured Aurora cluster with multiple instances, automated backups, enhanced monitoring, and optional RDS Proxy for improved connection management. The module handles the complexity of RDS configuration while providing consistent naming, tagging, and security settings.

## Features

- Creates Aurora PostgreSQL clusters with configurable version and instance types
- Supports multiple DB instances with different instance classes
- Validates instance types against an allowed list of optimized choices
- Automatically sets up proper backup and maintenance windows based on region
- Creates and configures parameter groups for both cluster and instances
- Implements enhanced monitoring with appropriate IAM roles
- Stores DB credentials securely in AWS Secrets Manager
- Optional RDS Proxy setup with reader endpoint for connection pooling
- Configures CloudWatch alarms with SNS notifications for monitoring
- Performance Insights enabled by default for deep database performance analysis
- Intelligent calculation of maintenance windows based on region to minimize impact
- Enforces storage encryption with optional custom KMS keys
- Integrates with other security components (VPC security groups, IAM roles)
- Comprehensive set of outputs for reference in other resources

## Usage Examples

### Example 1:

This example creates a simple Aurora PostgreSQL cluster with two instances:

```hcl
module "basic_aurora_db" {
  source = "sparrow-tofu-modules/components/aws/rds"

  name_prefix = "myapp-dev"
  name        = "db"
  db_version  = "14.6"

  username = "dbadmin"
  password = "YourStrongPasswordHere" # In production, use a secure method to handle this

  subnet_ids         = ["subnet-abc123", "subnet-def456", "subnet-ghi789"]
  security_group_ids = ["sg-123abc"]

  # Create a small writer instance and a reader instance
  instances = [
    {
      id    = "writer"
      class = "db.r6g.large"
    },
    {
      id    = "reader"
      class = "db.r6g.large"
      az    = "us-west-2b" # Specify the AZ (optional)
    }
  ]

  # Simple parameter overrides
  cluster_parameters = [
    {
      name  = "rds.force_ssl"
      value = "1"
    }
  ]

  tags = {
    Environment = "development"
    Project     = "MyApplication"
    ManagedBy   = "Terraform"
  }
}

# Output connection information
output "db_endpoint" {
  value = module.basic_aurora_db.cluster_writer_endpoint
}

output "db_reader_endpoint" {
  value = module.basic_aurora_db.cluster_reader_endpoint
}
```

### Example 2:

This example demonstrates a production-grade Aurora PostgreSQL setup with multiple instances, custom parameters, RDS Proxy, and configured monitoring:

```hcl
module "production_aurora_db" {
  source = "sparrow-tofu-modules/components/aws/rds"

  name_prefix = "production"
  name        = "main-db"
  db_version  = "14.6"

  username = "master_user"
  password = var.database_password # Retrieved securely from a variable

  subnet_ids         = data.aws_subnets.database.ids
  security_group_ids = [aws_security_group.database.id]

  # Use a custom KMS key for encryption
  kms_key_id = aws_kms_key.database_encryption.arn

  # Configure multiple instances with different sizes
  instances = [
    {
      id    = "writer"
      class = "db.r6g.2xlarge" # Larger instance for primary
    },
    {
      id    = "reader1"
      class = "db.r6g.xlarge"  # Medium reader
      az    = "us-east-1a"
    },
    {
      id    = "reader2"
      class = "db.r6g.xlarge"  # Medium reader
      az    = "us-east-1b"
    },
    {
      id    = "analytics"
      class = "db.r6g.2xlarge" # Larger reader for analytics workloads
      az    = "us-east-1c"
    }
  ]

  # Configure RDS Proxy for connection pooling
  rds_proxy_config = {
    enabled             = true
    debug_logging       = true
    idle_client_timeout = 1800
  }

  # Extensive parameter customization
  cluster_parameters = [
    {
      name  = "rds.force_ssl"
      value = "1"
    },
    {
      name  = "log_statement"
      value = "ddl"
    },
    {
      name  = "shared_buffers"
      value = "{DBInstanceClassMemory/32768}"
    },
    {
      name  = "max_connections"
      value = "1000"
    }
  ]

  instance_parameters = [
    {
      name  = "log_min_duration_statement"
      value = "1000"
    },
    {
      name  = "log_statement"
      value = "none"
    }
  ]

  # Configure monitoring alerts
  alarms_url = "https://hooks.slack.com/services/TXXXXXXXX/BXXXXXXXX/XXXXXXXX"

  # Optional: custom maintenance windows (otherwise calculated automatically)
  preferred_maintenance_window = "Sun:02:00-Sun:04:00"
  preferred_backup_window      = "01:00-02:00"

  tags = {
    Environment = "production"
    Service     = "main-database"
    CostCenter  = "database-12345"
    Owner       = "data-team"
    Backup      = "daily"
    ManagedBy   = "terraform"
  }
}

# Reference the endpoints with appropriate variables
output "writer_endpoint" {
  value       = module.production_aurora_db.cluster_writer_endpoint
  description = "Writer endpoint for direct Aurora access"
}

output "reader_endpoint" {
  value       = module.production_aurora_db.cluster_reader_endpoint
  description = "Reader endpoint for direct Aurora access"
}

output "proxy_endpoint" {
  value       = module.production_aurora_db.proxy_endpoint
  description = "RDS Proxy endpoint for connection pooling (writer)"
}

output "proxy_reader_endpoint" {
  value       = module.production_aurora_db.proxy_endpoint_reader
  description = "RDS Proxy reader endpoint for read-only connections"
}

# Get Secret ARN to use in application
data "aws_secretsmanager_secret" "db_credentials" {
  name = "${module.production_aurora_db.cluster_id}-rds-password"
}

output "db_secret_arn" {
  value = data.aws_secretsmanager_secret.db_credentials.arn
}
```

## Database Update and Upgrade Process

To perform database updates and upgrades with this module, you can use the following process to minimize downtime:

1. **Enable RDS Proxy**: First, enable the RDS Proxy if not already enabled by setting `rds_proxy_config.enabled = true`. This will create a proxy layer that helps manage connections during failovers.

2. **Add New Instance**: Add a new instance with the updated configuration or version to your `instances` list. Make sure to assign it a new unique ID.

   ```hcl
   instances = [
     {
       id    = "writer"
       class = "db.r6g.large"
     },
     {
       id    = "reader"
       class = "db.r6g.large"
     },
     {
       id    = "new-writer"  # New instance with updated config
       class = "db.r6g.xlarge"
     }
   ]
   ```

3. **Promote New Instance**: Once the new instance is created and properly synchronized, perform a failover to make the new instance the writer. This can be done using the AWS Management Console or CLI:

   ```bash
   aws rds failover-db-cluster --db-cluster-identifier <cluster-id> --target-db-instance-identifier <new-instance-id>
   ```

4. **Remove Old Instance**: After confirming the new instance is functioning correctly as the writer, remove the old instance from your configuration:

   ```hcl
   instances = [
     {
       id    = "new-writer"  # Now the main writer
       class = "db.r6g.xlarge"
     },
     {
       id    = "reader"
       class = "db.r6g.large"
     }
   ]
   ```

5. **Apply Changes**: Apply the Terraform changes to delete the old instance while keeping the cluster and its data intact.

The RDS Proxy provides connection pooling and minimizes disruption during failovers by maintaining persistent connections to the database. Applications connect to the proxy endpoints rather than directly to the cluster, allowing seamless switching between instances during upgrades.

This process allows for zero-downtime or minimal-downtime upgrades for both minor version updates and instance class changes.
