# sparrow-tofu-modules/components/aws/s3/outputs.tf

output "bucket_details" {
  description = "Details of all created S3 buckets."
  value = {
    for k, bucket in aws_s3_bucket.this : k => {
      id                          = bucket.id
      arn                         = bucket.arn
      bucket_domain_name          = bucket.bucket_domain_name
      bucket_regional_domain_name = bucket.bucket_regional_domain_name
      hosted_zone_id              = bucket.hosted_zone_id
      region                      = bucket.region
      tags                        = bucket.tags
    }
  }
}

output "bucket_names" {
  description = "Map of bucket keys to their full names (including prefix)."
  value       = { for k, bucket in aws_s3_bucket.this : k => bucket.id }
}

output "bucket_arns" {
  description = "Map of bucket keys to their ARNs."
  value       = { for k, bucket in aws_s3_bucket.this : k => bucket.arn }
}

output "bucket_domain_names" {
  description = "Map of bucket keys to their domain names."
  value       = { for k, bucket in aws_s3_bucket.this : k => bucket.bucket_domain_name }
}

output "bucket_regional_domain_names" {
  description = "Map of bucket keys to their regional domain names."
  value       = { for k, bucket in aws_s3_bucket.this : k => bucket.bucket_regional_domain_name }
}

output "bucket_hosted_zone_ids" {
  description = "Map of bucket keys to their hosted zone IDs."
  value       = { for k, bucket in aws_s3_bucket.this : k => bucket.hosted_zone_id }
}

# Convenience outputs for common use cases
output "bucket_ids" {
  description = "Map of bucket keys to their IDs (same as names)."
  value       = { for k, bucket in aws_s3_bucket.this : k => bucket.id }
}

output "bucket_regions" {
  description = "Map of bucket keys to their regions."
  value       = { for k, bucket in aws_s3_bucket.this : k => bucket.region }
}

# Application configuration output
output "bucket_configuration" {
  description = "Configuration values for applications using these buckets."
  value = {
    bucket_names = { for k, bucket in aws_s3_bucket.this : k => bucket.id }
    bucket_arns  = { for k, bucket in aws_s3_bucket.this : k => bucket.arn }
    bucket_urls = {
      for k, bucket in aws_s3_bucket.this : k => "https://${bucket.bucket_domain_name}"
    }
    regional_urls = {
      for k, bucket in aws_s3_bucket.this : k => "https://${bucket.bucket_regional_domain_name}"
    }
  }
}