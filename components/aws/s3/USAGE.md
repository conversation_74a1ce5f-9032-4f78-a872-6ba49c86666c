# S3 Module Usage Guide

This guide provides comprehensive examples and best practices for using the S3 module in your Terraform configurations.

## Quick Start

### 1. Simple Bucket (No Prefix)

```hcl
module "s3" {
  source = "sparrow-tofu-modules/components/aws/s3"

  buckets = [
    {
      name = "my-app-documents"
      versioning = {
        enabled = true
      }
    }
  ]

  tags = {
    Environment = "development"
    Project     = "myapp"
  }
}
```

### 2. Organized Buckets with Prefix

```hcl
module "s3" {
  source = "sparrow-tofu-modules/components/aws/s3"

  name_prefix = "myapp-dev"  # Optional: adds prefix to all bucket names

  buckets = [
    {
      name = "documents"  # becomes: myapp-dev-documents
      versioning = {
        enabled = true
      }
    }
  ]

  tags = {
    Environment = "development"
    Project     = "myapp"
  }
}
```

### 3. When to Use Prefixes vs Simple Names

**Use Simple Names (no prefix) when:**
- Single application or simple setup
- You want full control over bucket naming
- Quick development or testing
- Bucket names are already descriptive

**Use Prefixes when:**
- Multiple environments (dev, staging, prod)
- Multiple applications sharing AWS account
- Organizational naming standards
- Team-based resource separation

```hcl
# Simple approach - direct naming
module "s3_simple" {
  source = "sparrow-tofu-modules/components/aws/s3"

  buckets = [
    {
      name = "user-profile-images"
    },
    {
      name = "application-backups"
    }
  ]
}

# Organized approach - with prefix
module "s3_organized" {
  source = "sparrow-tofu-modules/components/aws/s3"

  name_prefix = "ecommerce-prod"

  buckets = [
    {
      name = "images"     # becomes: ecommerce-prod-images
    },
    {
      name = "backups"    # becomes: ecommerce-prod-backups
    }
  ]
}
```

## Common Use Cases

### Document Storage Application

```hcl
module "document_storage" {
  source = "sparrow-tofu-modules/components/aws/s3"

  name_prefix = "docapp"

  buckets = [
    {
      name = "uploads"
      versioning = {
        enabled = true
      }
      cors_configuration = {
        cors_rules = [
          {
            allowed_headers = ["*"]
            allowed_methods = ["GET", "PUT", "POST"]
            allowed_origins = ["https://app.example.com"]
            expose_headers  = ["ETag"]
            max_age_seconds = 3000
          }
        ]
      }
      notification_configuration = {
        queue = [
          {
            queue_arn     = aws_sqs_queue.document_processing.arn
            events        = ["s3:ObjectCreated:*"]
            filter_prefix = "uploads/"
          }
        ]
      }
      tags = {
        Purpose = "user-uploads"
      }
    },
    {
      name = "processed"
      versioning = {
        enabled = true
      }
      lifecycle_configuration = {
        rules = [
          {
            id     = "archive-old-documents"
            status = "Enabled"
            transition = [
              {
                days          = 90
                storage_class = "STANDARD_IA"
              },
              {
                days          = 365
                storage_class = "GLACIER"
              }
            ]
          }
        ]
      }
      tags = {
        Purpose = "processed-documents"
      }
    }
  ]

  tags = {
    Application = "document-management"
    Team        = "backend"
  }
}
```

### Static Website Hosting

```hcl
module "static_website" {
  source = "sparrow-tofu-modules/components/aws/s3"

  name_prefix = "website"

  buckets = [
    {
      name = "content"
      
      # Allow public read for website content
      block_public_acls       = false
      block_public_policy     = false
      ignore_public_acls      = false
      restrict_public_buckets = false
      
      bucket_policy = jsonencode({
        Version = "2012-10-17"
        Statement = [
          {
            Sid       = "PublicReadGetObject"
            Effect    = "Allow"
            Principal = "*"
            Action    = "s3:GetObject"
            Resource  = "arn:aws:s3:::website-content/*"
          }
        ]
      })
      
      cors_configuration = {
        cors_rules = [
          {
            allowed_headers = ["*"]
            allowed_methods = ["GET", "HEAD"]
            allowed_origins = ["*"]
            max_age_seconds = 3000
          }
        ]
      }
      
      tags = {
        Purpose = "static-website"
      }
    },
    {
      name = "logs"
      lifecycle_configuration = {
        rules = [
          {
            id     = "delete-old-logs"
            status = "Enabled"
            expiration = {
              days = 90
            }
          }
        ]
      }
      tags = {
        Purpose = "access-logs"
      }
    }
  ]

  tags = {
    Application = "static-website"
    Environment = "production"
  }
}
```

### Data Lake Architecture

```hcl
module "data_lake" {
  source = "sparrow-tofu-modules/components/aws/s3"

  name_prefix = "datalake"

  buckets = [
    {
      name = "raw-data"
      versioning = {
        enabled = true
      }
      kms_key_arn = aws_kms_key.data_lake.arn
      
      lifecycle_configuration = {
        rules = [
          {
            id     = "intelligent-tiering"
            status = "Enabled"
            transition = [
              {
                days          = 0
                storage_class = "INTELLIGENT_TIERING"
              }
            ]
          }
        ]
      }
      
      notification_configuration = {
        lambda_function = [
          {
            lambda_function_arn = aws_lambda_function.data_processor.arn
            events              = ["s3:ObjectCreated:*"]
            filter_prefix       = "incoming/"
          }
        ]
      }
      
      tags = {
        DataClassification = "sensitive"
        Purpose           = "raw-data-ingestion"
      }
    },
    {
      name = "processed-data"
      versioning = {
        enabled = true
      }
      kms_key_arn = aws_kms_key.data_lake.arn
      
      lifecycle_configuration = {
        rules = [
          {
            id     = "partition-management"
            status = "Enabled"
            filter = {
              prefix = "year="
            }
            transition = [
              {
                days          = 30
                storage_class = "STANDARD_IA"
              },
              {
                days          = 90
                storage_class = "GLACIER"
              }
            ]
          }
        ]
      }
      
      tags = {
        DataClassification = "processed"
        Purpose           = "analytics-ready-data"
      }
    },
    {
      name = "archive"
      versioning = {
        enabled = false
      }
      kms_key_arn = aws_kms_key.data_lake.arn
      
      lifecycle_configuration = {
        rules = [
          {
            id     = "immediate-archive"
            status = "Enabled"
            transition = [
              {
                days          = 0
                storage_class = "DEEP_ARCHIVE"
              }
            ]
          }
        ]
      }
      
      tags = {
        DataClassification = "archived"
        Purpose           = "long-term-storage"
      }
    }
  ]

  tags = {
    Application = "data-lake"
    Environment = "production"
    Compliance  = "required"
  }
}
```

### Backup and Disaster Recovery

```hcl
module "backup_storage" {
  source = "sparrow-tofu-modules/components/aws/s3"

  name_prefix = "backup-dr"

  buckets = [
    {
      name = "database-backups"
      versioning = {
        enabled = true
      }
      kms_key_arn = aws_kms_key.backup.arn
      
      lifecycle_configuration = {
        rules = [
          {
            id     = "backup-retention"
            status = "Enabled"
            transition = [
              {
                days          = 30
                storage_class = "STANDARD_IA"
              },
              {
                days          = 90
                storage_class = "GLACIER"
              },
              {
                days          = 365
                storage_class = "DEEP_ARCHIVE"
              }
            ]
            expiration = {
              days = 2555  # 7 years
            }
            noncurrent_version_expiration = {
              noncurrent_days = 30
            }
          }
        ]
      }
      
      notification_configuration = {
        topic = [
          {
            topic_arn = aws_sns_topic.backup_alerts.arn
            events    = ["s3:ObjectCreated:*", "s3:ObjectRemoved:*"]
          }
        ]
      }
      
      tags = {
        Purpose     = "database-backups"
        Criticality = "high"
        Retention   = "7-years"
      }
    },
    {
      name = "application-backups"
      versioning = {
        enabled = true
      }
      
      lifecycle_configuration = {
        rules = [
          {
            id     = "app-backup-lifecycle"
            status = "Enabled"
            filter = {
              tags = {
                BackupType = "application"
              }
            }
            transition = [
              {
                days          = 7
                storage_class = "STANDARD_IA"
              },
              {
                days          = 30
                storage_class = "GLACIER"
              }
            ]
            expiration = {
              days = 365
            }
          }
        ]
      }
      
      tags = {
        Purpose   = "application-backups"
        Retention = "1-year"
      }
    }
  ]

  tags = {
    Application = "backup-system"
    Environment = "production"
    Compliance  = "sox-compliant"
  }
}
```

## Best Practices

### 1. Naming Best Practices

```hcl
# Good naming examples without prefix
module "s3_good_naming" {
  source = "sparrow-tofu-modules/components/aws/s3"

  buckets = [
    # Descriptive and clear purpose
    { name = "user-profile-images" },
    { name = "application-logs" },
    { name = "customer-documents" },
    { name = "backup-database-dumps" },

    # Include environment when not using prefix
    { name = "prod-static-assets" },
    { name = "staging-file-uploads" }
  ]
}

# Good naming examples with prefix
module "s3_with_prefix" {
  source = "sparrow-tofu-modules/components/aws/s3"

  name_prefix = "ecommerce-prod"

  buckets = [
    # Short, clear names since prefix provides context
    { name = "images" },        # becomes: ecommerce-prod-images
    { name = "documents" },     # becomes: ecommerce-prod-documents
    { name = "backups" },       # becomes: ecommerce-prod-backups
    { name = "logs" }           # becomes: ecommerce-prod-logs
  ]
}

# Avoid these naming patterns
module "s3_bad_naming" {
  source = "sparrow-tofu-modules/components/aws/s3"

  buckets = [
    # Too generic
    { name = "bucket1" },
    { name = "data" },
    { name = "files" },

    # Too long
    { name = "my-application-user-uploaded-profile-images-bucket" },

    # Unclear purpose
    { name = "temp-bucket" },
    { name = "test" },
    { name = "misc" }
  ]
}
```

### 2. Environment-Specific Configuration

```hcl
locals {
  environment_config = {
    development = {
      versioning_enabled = false
      lifecycle_enabled  = false
      kms_encryption    = false
      use_prefix        = false  # Simple naming for dev
    }
    staging = {
      versioning_enabled = true
      lifecycle_enabled  = true
      kms_encryption    = false
      use_prefix        = true   # Organized naming for staging
    }
    production = {
      versioning_enabled = true
      lifecycle_enabled  = true
      kms_encryption    = true
      use_prefix        = true   # Organized naming for production
    }
  }
}

# Development - simple naming
module "s3_dev" {
  count  = var.environment == "development" ? 1 : 0
  source = "sparrow-tofu-modules/components/aws/s3"

  buckets = [
    {
      name = "dev-app-data"
      versioning = {
        enabled = local.environment_config[var.environment].versioning_enabled
      }
    }
  ]

  tags = {
    Environment = var.environment
  }
}

# Staging/Production - organized naming
module "s3_organized" {
  count  = var.environment != "development" ? 1 : 0
  source = "sparrow-tofu-modules/components/aws/s3"

  name_prefix = "${var.environment}-myapp"

  buckets = [
    {
      name = "data"
      versioning = {
        enabled = local.environment_config[var.environment].versioning_enabled
      }
      kms_key_arn = local.environment_config[var.environment].kms_encryption ? aws_kms_key.s3[0].arn : null

      lifecycle_configuration = local.environment_config[var.environment].lifecycle_enabled ? {
        rules = [
          {
            id     = "cost-optimization"
            status = "Enabled"
            transition = [
              {
                days          = 30
                storage_class = "STANDARD_IA"
              }
            ]
          }
        ]
      } : null
    }
  ]

  tags = {
    Environment = var.environment
  }
}
```

### 2. Using Outputs in Applications

```hcl
# After creating buckets, use outputs for application configuration
output "s3_config" {
  value = {
    bucket_names = module.s3.bucket_names
    bucket_arns  = module.s3.bucket_arns
  }
}

# In your application configuration
resource "aws_ssm_parameter" "s3_bucket_names" {
  for_each = module.s3.bucket_names

  name  = "/app/s3/${each.key}/name"
  type  = "String"
  value = each.value

  tags = var.tags
}
```

## Troubleshooting

### Common Issues

1. **Bucket Name Conflicts**
   - Ensure your name_prefix is unique globally
   - S3 bucket names must be globally unique

2. **Permission Issues**
   - Check IAM policies for S3 access
   - Verify bucket policies don't conflict with IAM

3. **Notification Setup**
   - Ensure SQS/SNS/Lambda resources exist before bucket creation
   - Check resource policies allow S3 to publish

4. **CORS Issues**
   - Verify allowed origins match your application domains
   - Check that methods and headers are correctly specified

### Validation Errors

The module includes comprehensive validation. Common validation errors:

- **Empty bucket names**: Bucket name cannot be empty
- **Invalid lifecycle status**: Must be "Enabled" or "Disabled"
- **Invalid CORS methods**: Must be GET, PUT, POST, DELETE, or HEAD
- **Missing notification targets**: Must have at least one queue, topic, or lambda function

## Migration Guide

### From Single Bucket Module

If you're currently using a single-bucket S3 module:

```hcl
# Old single bucket usage
module "s3_old" {
  source = "old-s3-module"
  
  bucket_name = "my-bucket"
  versioning_enabled = true
}

# New multi-bucket usage
module "s3_new" {
  source = "sparrow-tofu-modules/components/aws/s3"
  
  name_prefix = "myapp"
  
  buckets = [
    {
      name = "bucket"  # Will become "myapp-bucket"
      versioning = {
        enabled = true
      }
    }
  ]
}
```

## Support

For issues with the S3 module:
1. Check the validation errors in the plan output
2. Verify your AWS permissions
3. Review the AWS S3 documentation for feature limitations
4. Check the module source code for advanced customization options
