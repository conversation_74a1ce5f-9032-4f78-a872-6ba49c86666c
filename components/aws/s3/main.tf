# ------------------------------------------------------------------------------
# S3 Terraform Module
# This module creates S3 buckets with comprehensive configurations and security best practices
# ------------------------------------------------------------------------------

terraform {
  required_providers {
    aws = {
      source = "hashicorp/aws"
    }
  }
}

# ------------------------------------------------------------------------------
# Local Values
# ------------------------------------------------------------------------------

locals {
  # Create bucket names with optional prefix
  bucket_names = {
    for bucket in var.buckets : bucket.name => var.name_prefix != "" ? "${var.name_prefix}-${bucket.name}" : bucket.name
  }
}

# Create S3 buckets
resource "aws_s3_bucket" "this" {
  for_each = { for idx, bucket in var.buckets : bucket.name => bucket }

  bucket        = local.bucket_names[each.value.name]
  force_destroy = lookup(each.value, "force_destroy", false)

  tags = merge(
    var.tags,
    lookup(each.value, "tags", {}),
    {
      Name      = local.bucket_names[each.value.name]
      ManagedBy = "terraform"
      Module    = "s3"
    }
  )
}

# Configure bucket versioning
resource "aws_s3_bucket_versioning" "this" {
  for_each = { for k, bucket in var.buckets : k => bucket if lookup(bucket, "versioning", null) != null }

  bucket = aws_s3_bucket.this[each.key].id

  versioning_configuration {
    status = each.value.versioning.enabled ? "Enabled" : "Disabled"
  }
}

# Configure server-side encryption
resource "aws_s3_bucket_server_side_encryption_configuration" "this" {
  for_each = aws_s3_bucket.this

  bucket = each.value.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm     = lookup(var.buckets[each.key], "kms_key_arn", null) != null ? "aws:kms" : "AES256"
      kms_master_key_id = lookup(var.buckets[each.key], "kms_key_arn", null)
    }
    bucket_key_enabled = lookup(var.buckets[each.key], "bucket_key_enabled", true)
  }
}

# Configure public access block
resource "aws_s3_bucket_public_access_block" "this" {
  for_each = aws_s3_bucket.this

  bucket = each.value.id

  block_public_acls       = lookup(var.buckets[each.key], "block_public_acls", true)
  block_public_policy     = lookup(var.buckets[each.key], "block_public_policy", true)
  ignore_public_acls      = lookup(var.buckets[each.key], "ignore_public_acls", true)
  restrict_public_buckets = lookup(var.buckets[each.key], "restrict_public_buckets", true)
}

# Configure lifecycle rules
resource "aws_s3_bucket_lifecycle_configuration" "this" {
  for_each = { for k, bucket in var.buckets : k => bucket if lookup(bucket, "lifecycle_configuration", null) != null }

  bucket = aws_s3_bucket.this[each.key].id

  dynamic "rule" {
    for_each = each.value.lifecycle_configuration.rules
    content {
      id     = rule.value.id
      status = rule.value.status

      dynamic "filter" {
        for_each = lookup(rule.value, "filter", null) != null ? [rule.value.filter] : []
        content {
          prefix = lookup(filter.value, "prefix", null)

          dynamic "tag" {
            for_each = lookup(filter.value, "tags", {})
            content {
              key   = tag.key
              value = tag.value
            }
          }
        }
      }

      dynamic "transition" {
        for_each = lookup(rule.value, "transition", [])
        content {
          days          = lookup(transition.value, "days", null)
          date          = lookup(transition.value, "date", null)
          storage_class = transition.value.storage_class
        }
      }

      dynamic "expiration" {
        for_each = lookup(rule.value, "expiration", null) != null ? [rule.value.expiration] : []
        content {
          days                         = lookup(expiration.value, "days", null)
          date                         = lookup(expiration.value, "date", null)
          expired_object_delete_marker = lookup(expiration.value, "expired_object_delete_marker", null)
        }
      }

      dynamic "noncurrent_version_expiration" {
        for_each = lookup(rule.value, "noncurrent_version_expiration", null) != null ? [rule.value.noncurrent_version_expiration] : []
        content {
          noncurrent_days = noncurrent_version_expiration.value.noncurrent_days
        }
      }

      dynamic "noncurrent_version_transition" {
        for_each = lookup(rule.value, "noncurrent_version_transition", [])
        content {
          noncurrent_days = noncurrent_version_transition.value.noncurrent_days
          storage_class   = noncurrent_version_transition.value.storage_class
        }
      }
    }
  }
}

# Configure bucket policies
resource "aws_s3_bucket_policy" "this" {
  for_each = { for k, bucket in var.buckets : k => bucket if lookup(bucket, "bucket_policy", null) != null }

  bucket = aws_s3_bucket.this[each.key].id
  policy = each.value.bucket_policy
}

# Configure CORS
resource "aws_s3_bucket_cors_configuration" "this" {
  for_each = { for k, bucket in var.buckets : k => bucket if lookup(bucket, "cors_configuration", null) != null }

  bucket = aws_s3_bucket.this[each.key].id

  dynamic "cors_rule" {
    for_each = each.value.cors_configuration.cors_rules
    content {
      id              = lookup(cors_rule.value, "id", null)
      allowed_headers = lookup(cors_rule.value, "allowed_headers", [])
      allowed_methods = cors_rule.value.allowed_methods
      allowed_origins = cors_rule.value.allowed_origins
      expose_headers  = lookup(cors_rule.value, "expose_headers", [])
      max_age_seconds = lookup(cors_rule.value, "max_age_seconds", null)
    }
  }
}

# Configure S3 bucket notifications
resource "aws_s3_bucket_notification" "this" {
  for_each = { for k, bucket in var.buckets : k => bucket if lookup(bucket, "notification_configuration", null) != null }

  bucket = aws_s3_bucket.this[each.key].id

  dynamic "queue" {
    for_each = lookup(each.value.notification_configuration, "queue", [])
    content {
      queue_arn     = queue.value.queue_arn
      events        = queue.value.events
      filter_prefix = lookup(queue.value, "filter_prefix", null)
      filter_suffix = lookup(queue.value, "filter_suffix", null)
    }
  }

  dynamic "topic" {
    for_each = lookup(each.value.notification_configuration, "topic", [])
    content {
      topic_arn     = topic.value.topic_arn
      events        = topic.value.events
      filter_prefix = lookup(topic.value, "filter_prefix", null)
      filter_suffix = lookup(topic.value, "filter_suffix", null)
    }
  }

  dynamic "lambda_function" {
    for_each = lookup(each.value.notification_configuration, "lambda_function", [])
    content {
      lambda_function_arn = lambda_function.value.lambda_function_arn
      events              = lambda_function.value.events
      filter_prefix       = lookup(lambda_function.value, "filter_prefix", null)
      filter_suffix       = lookup(lambda_function.value, "filter_suffix", null)
    }
  }
}

# Configure bucket logging
resource "aws_s3_bucket_logging" "this" {
  for_each = { for k, bucket in var.buckets : k => bucket if lookup(bucket, "logging", null) != null }

  bucket = aws_s3_bucket.this[each.key].id

  target_bucket = each.value.logging.target_bucket
  target_prefix = lookup(each.value.logging, "target_prefix", "")
}