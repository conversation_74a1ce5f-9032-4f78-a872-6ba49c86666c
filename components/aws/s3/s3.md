# S3 Module

This module creates and manages multiple S3 buckets with comprehensive configurations and security best practices.

## Features

- **Multiple Buckets**: Create multiple S3 buckets in a single module call
- **Security First**: Encryption by default (AES256 or KMS), public access blocking
- **Flexible Configuration**: Versioning, lifecycle policies, CORS, notifications
- **Event Notifications**: Support for SQS, SNS, and Lambda notifications
- **Access Logging**: Configure access logging to other S3 buckets
- **Lifecycle Management**: Comprehensive lifecycle rules with transitions and expiration
- **Consistent Naming**: Automatic resource naming with configurable prefixes
- **Comprehensive Tagging**: Consistent tagging across all resources
- **Production Ready**: Built-in validation and security best practices

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.4.6 |
| aws | >= 5.0 |

## Providers

| Name | Version |
|------|---------|
| aws | >= 5.0 |

## Resources

| Name | Type |
|------|------|
| aws_s3_bucket | resource |
| aws_s3_bucket_versioning | resource |
| aws_s3_bucket_server_side_encryption_configuration | resource |
| aws_s3_bucket_public_access_block | resource |
| aws_s3_bucket_lifecycle_configuration | resource |
| aws_s3_bucket_policy | resource |
| aws_s3_bucket_cors_configuration | resource |
| aws_s3_bucket_notification | resource |
| aws_s3_bucket_logging | resource |

## Usage Examples

### Example 1: Basic S3 Buckets with Prefix

```hcl
module "s3" {
  source = "sparrow-tofu-modules/components/aws/s3"

  name_prefix = "myapp-prod"

  buckets = [
    {
      name = "documents"
      versioning = {
        enabled = true
      }
      tags = {
        Purpose = "document-storage"
      }
    },
    {
      name = "backups"
      versioning = {
        enabled = true
      }
      lifecycle_configuration = {
        rules = [
          {
            id     = "archive-old-backups"
            status = "Enabled"
            transition = [
              {
                days          = 30
                storage_class = "STANDARD_IA"
              },
              {
                days          = 90
                storage_class = "GLACIER"
              }
            ]
            expiration = {
              days = 365
            }
          }
        ]
      }
      tags = {
        Purpose = "backup-storage"
      }
    }
  ]

  tags = {
    Environment = "production"
    Project     = "myapp"
    ManagedBy   = "terraform"
  }
}
```

**Result**: Creates buckets named `myapp-prod-documents` and `myapp-prod-backups`.

### Example 1b: Simple S3 Buckets Without Prefix

```hcl
module "s3_simple" {
  source = "sparrow-tofu-modules/components/aws/s3"

  buckets = [
    {
      name = "my-app-user-documents"
      versioning = {
        enabled = true
      }
      tags = {
        Purpose = "document-storage"
      }
    },
    {
      name = "application-backup-storage"
      versioning = {
        enabled = true
      }
      lifecycle_configuration = {
        rules = [
          {
            id     = "archive-old-backups"
            status = "Enabled"
            transition = [
              {
                days          = 30
                storage_class = "STANDARD_IA"
              }
            ]
            expiration = {
              days = 365
            }
          }
        ]
      }
      tags = {
        Purpose = "backup-storage"
      }
    }
  ]

  tags = {
    Environment = "development"
    Application = "my-app"
  }
}
```

**Result**: Creates buckets named `my-app-user-documents` and `application-backup-storage`.

### Example 2: Advanced Configuration with Notifications

```hcl
module "s3_advanced" {
  source = "sparrow-tofu-modules/components/aws/s3"

  name_prefix = "advanced-app"

  buckets = [
    {
      name          = "uploads"
      force_destroy = false

      versioning = {
        enabled = true
      }

      # KMS encryption
      kms_key_arn = aws_kms_key.s3.arn

      # Lifecycle management
      lifecycle_configuration = {
        rules = [
          {
            id     = "intelligent-tiering"
            status = "Enabled"
            filter = {
              prefix = "documents/"
            }
            transition = [
              {
                days          = 0
                storage_class = "INTELLIGENT_TIERING"
              }
            ]
          },
          {
            id     = "cleanup-temp"
            status = "Enabled"
            filter = {
              prefix = "temp/"
            }
            expiration = {
              days = 7
            }
          }
        ]
      }

      # Event notifications
      notification_configuration = {
        queue = [
          {
            queue_arn     = aws_sqs_queue.uploads.arn
            events        = ["s3:ObjectCreated:*"]
            filter_prefix = "uploads/"
          }
        ]
        topic = [
          {
            topic_arn     = aws_sns_topic.alerts.arn
            events        = ["s3:ObjectRemoved:*"]
          }
        ]
      }

      # CORS for web uploads
      cors_configuration = {
        cors_rules = [
          {
            allowed_headers = ["*"]
            allowed_methods = ["GET", "PUT", "POST"]
            allowed_origins = ["https://app.example.com"]
            expose_headers  = ["ETag"]
            max_age_seconds = 3000
          }
        ]
      }

      # Access logging
      logging = {
        target_bucket = "advanced-app-access-logs"
        target_prefix = "uploads-access/"
      }

      tags = {
        Purpose     = "user-uploads"
        Criticality = "high"
      }
    }
  ]

  tags = {
    Environment = "production"
    Application = "advanced-app"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| name_prefix | A prefix for all resource names to ensure uniqueness and grouping | `string` | `""` | no |
| tags | A map of tags to assign to all created resources | `map(string)` | `{}` | no |
| buckets | List of S3 buckets to create | `list(object)` | `[]` | no |

### Bucket Configuration

Each bucket in the `buckets` list supports the following attributes:

- `name` (required): Name of the bucket (will be prefixed with `name_prefix` if provided)
- `force_destroy` (optional): Allow destruction of non-empty bucket (defaults to `false`)
- `versioning` (optional): Versioning configuration
- `kms_key_arn` (optional): KMS key ARN for encryption (uses AES256 if not specified)
- `bucket_key_enabled` (optional): Enable S3 bucket key for KMS (defaults to `true`)
- `block_public_acls` (optional): Block public ACLs (defaults to `true`)
- `block_public_policy` (optional): Block public bucket policies (defaults to `true`)
- `ignore_public_acls` (optional): Ignore public ACLs (defaults to `true`)
- `restrict_public_buckets` (optional): Restrict public bucket policies (defaults to `true`)
- `lifecycle_configuration` (optional): Lifecycle rules configuration
- `bucket_policy` (optional): Bucket policy as JSON string
- `cors_configuration` (optional): CORS configuration
- `notification_configuration` (optional): Event notification configuration
- `logging` (optional): Access logging configuration
- `tags` (optional): Additional tags specific to this bucket

### Naming Behavior

The module supports flexible naming:

- **With name_prefix**: `name_prefix = "myapp"` + `name = "documents"` → `myapp-documents`
- **Without name_prefix**: `name = "my-app-documents"` → `my-app-documents`

Choose the approach that best fits your naming conventions and organizational needs.

## Outputs

| Name | Description |
|------|-------------|
| bucket_details | Details of all created S3 buckets |
| bucket_names | Map of bucket keys to their full names |
| bucket_arns | Map of bucket keys to their ARNs |
| bucket_domain_names | Map of bucket keys to their domain names |
| bucket_regional_domain_names | Map of bucket keys to their regional domain names |
| bucket_hosted_zone_ids | Map of bucket keys to their hosted zone IDs |
| bucket_ids | Map of bucket keys to their IDs |
| bucket_regions | Map of bucket keys to their regions |
| bucket_configuration | Configuration values for applications |

## Configuration Details

### Lifecycle Rules

Lifecycle rules help manage object storage costs by automatically transitioning objects to different storage classes or deleting them:

```hcl
lifecycle_configuration = {
  rules = [
    {
      id     = "cost-optimization"
      status = "Enabled"
      filter = {
        prefix = "documents/"
        tags = {
          Archive = "true"
        }
      }
      transition = [
        {
          days          = 30
          storage_class = "STANDARD_IA"
        },
        {
          days          = 90
          storage_class = "GLACIER"
        },
        {
          days          = 365
          storage_class = "DEEP_ARCHIVE"
        }
      ]
      expiration = {
        days = 2555  # 7 years
      }
      noncurrent_version_expiration = {
        noncurrent_days = 90
      }
    }
  ]
}
```

### Event Notifications

Configure S3 to send notifications when objects are created, deleted, or modified:

```hcl
notification_configuration = {
  queue = [
    {
      queue_arn     = "arn:aws:sqs:us-west-2:123456789012:s3-events"
      events        = ["s3:ObjectCreated:*", "s3:ObjectRemoved:*"]
      filter_prefix = "uploads/"
      filter_suffix = ".jpg"
    }
  ]
  topic = [
    {
      topic_arn = "arn:aws:sns:us-west-2:123456789012:s3-notifications"
      events    = ["s3:ObjectCreated:*"]
    }
  ]
  lambda_function = [
    {
      lambda_function_arn = "arn:aws:lambda:us-west-2:123456789012:function:ProcessS3Event"
      events              = ["s3:ObjectCreated:Put"]
      filter_prefix       = "images/"
    }
  ]
}
```

### CORS Configuration

Enable cross-origin requests for web applications:

```hcl
cors_configuration = {
  cors_rules = [
    {
      id              = "web-app-cors"
      allowed_headers = ["*"]
      allowed_methods = ["GET", "PUT", "POST", "DELETE", "HEAD"]
      allowed_origins = ["https://app.example.com", "https://www.example.com"]
      expose_headers  = ["ETag", "x-amz-meta-custom-header"]
      max_age_seconds = 3000
    }
  ]
}
```

### Security Best Practices

The module implements security best practices by default:

- **Encryption**: All buckets are encrypted by default (AES256 or KMS)
- **Public Access**: Public access is blocked by default
- **Versioning**: Can be enabled to protect against accidental deletion
- **Access Logging**: Can be configured to track access patterns
- **Bucket Policies**: Support for fine-grained access control

### Access Logging

Configure access logging to track requests to your bucket:

```hcl
logging = {
  target_bucket = "my-access-logs-bucket"
  target_prefix = "access-logs/my-app/"
}
```

## Module Summary

This S3 module provides a comprehensive solution for managing S3 buckets with enterprise-grade features:

- **Multi-bucket Support**: Create multiple buckets with different configurations
- **Security First**: Encryption and public access blocking by default
- **Cost Optimization**: Lifecycle rules for automatic storage class transitions
- **Event-Driven**: Support for SQS, SNS, and Lambda notifications
- **Web-Ready**: CORS configuration for web applications
- **Audit-Ready**: Access logging capabilities
- **Production-Ready**: Comprehensive validation and error handling

## Best Practices

1. **Use KMS encryption** for sensitive data
2. **Enable versioning** for important buckets
3. **Configure lifecycle rules** to optimize costs
4. **Set up notifications** for automated processing
5. **Use access logging** for security auditing
6. **Apply least privilege** with bucket policies
7. **Tag consistently** for cost allocation and management