# sparrow-tofu-modules/components/aws/s3/variables.tf

variable "name_prefix" {
  description = "A prefix for all resource names to ensure uniqueness and grouping. If not provided, only the bucket name will be used."
  type        = string
  default     = ""
  nullable    = false
}

variable "tags" {
  description = "A map of tags to assign to all created resources."
  type        = map(string)
  default     = {}
  nullable    = false
}

variable "buckets" {
  description = "List of S3 buckets to create."
  type = list(object({
    name          = string
    force_destroy = optional(bool, false)

    # Versioning configuration
    versioning = optional(object({
      enabled = bool
    }))

    # Encryption configuration
    kms_key_arn        = optional(string)
    bucket_key_enabled = optional(bool, true)

    # Public access block configuration
    block_public_acls       = optional(bool, true)
    block_public_policy     = optional(bool, true)
    ignore_public_acls      = optional(bool, true)
    restrict_public_buckets = optional(bool, true)

    # Lifecycle configuration
    lifecycle_configuration = optional(object({
      rules = list(object({
        id     = string
        status = string
        filter = optional(object({
          prefix = optional(string)
          tags   = optional(map(string), {})
        }))
        transition = optional(list(object({
          days          = optional(number)
          date          = optional(string)
          storage_class = string
        })), [])
        expiration = optional(object({
          days                         = optional(number)
          date                         = optional(string)
          expired_object_delete_marker = optional(bool)
        }))
        noncurrent_version_expiration = optional(object({
          noncurrent_days = number
        }))
        noncurrent_version_transition = optional(list(object({
          noncurrent_days = number
          storage_class   = string
        })), [])
      }))
    }))

    # Bucket policy (JSON string)
    bucket_policy = optional(string)

    # CORS configuration
    cors_configuration = optional(object({
      cors_rules = list(object({
        id              = optional(string)
        allowed_headers = optional(list(string), [])
        allowed_methods = list(string)
        allowed_origins = list(string)
        expose_headers  = optional(list(string), [])
        max_age_seconds = optional(number)
      }))
    }))

    # Notification configuration
    notification_configuration = optional(object({
      queue = optional(list(object({
        queue_arn     = string
        events        = list(string)
        filter_prefix = optional(string)
        filter_suffix = optional(string)
      })), [])
      topic = optional(list(object({
        topic_arn     = string
        events        = list(string)
        filter_prefix = optional(string)
        filter_suffix = optional(string)
      })), [])
      lambda_function = optional(list(object({
        lambda_function_arn = string
        events              = list(string)
        filter_prefix       = optional(string)
        filter_suffix       = optional(string)
      })), [])
    }))

    # Logging configuration
    logging = optional(object({
      target_bucket = string
      target_prefix = optional(string, "")
    }))

    # Additional tags specific to this bucket
    tags = optional(map(string), {})
  }))
  default = []

  validation {
    condition = alltrue([
      for bucket in var.buckets : length(bucket.name) > 0
    ])
    error_message = "Bucket name cannot be empty."
  }

  validation {
    condition = alltrue([
      for bucket in var.buckets : (
        bucket.lifecycle_configuration == null ||
        alltrue([
          for rule in bucket.lifecycle_configuration.rules :
          contains(["Enabled", "Disabled"], rule.status)
        ])
      )
    ])
    error_message = "Lifecycle rule status must be either 'Enabled' or 'Disabled'."
  }

  validation {
    condition = alltrue([
      for bucket in var.buckets : (
        bucket.cors_configuration == null ||
        alltrue([
          for rule in bucket.cors_configuration.cors_rules :
          alltrue([
            for method in rule.allowed_methods :
            contains(["GET", "PUT", "POST", "DELETE", "HEAD"], method)
          ])
        ])
      )
    ])
    error_message = "CORS allowed methods must be one of: GET, PUT, POST, DELETE, HEAD."
  }

  validation {
    condition = alltrue([
      for bucket in var.buckets : (
        bucket.notification_configuration == null ||
        (
          length(lookup(bucket.notification_configuration, "queue", [])) +
          length(lookup(bucket.notification_configuration, "topic", [])) +
          length(lookup(bucket.notification_configuration, "lambda_function", []))
        ) > 0
      )
    ])
    error_message = "Notification configuration must have at least one queue, topic, or lambda function."
  }
}