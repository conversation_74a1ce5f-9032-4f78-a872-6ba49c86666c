variable "name_prefix" {
  type        = string
  description = "Prefix to be used for all resource names"
}

variable "tags" {
  type        = map(string)
  description = "Tags to be applied to all resources"
  default     = {}
}

variable "queues" {
  type = list(object({
    name       = string
    fifo_queue = optional(bool, false)
    enable_dlq = optional(bool, false)

    # Optional queue settings
    delay_seconds             = optional(number)
    max_message_size          = optional(number)
    message_retention_seconds = optional(number)
    receive_wait_time_seconds = optional(number)
    visibility_timeout        = optional(number)

    # FIFO specific settings
    # deduplication_scope         = optional(string)
    # fifo_throughput_limit       = optional(string)
    content_based_deduplication = optional(bool)

    # DLQ specific settings
    dlq_settings = optional(object({
      delay_seconds             = optional(number)
      max_message_size          = optional(number)
      message_retention_seconds = optional(number)
      receive_wait_time_seconds = optional(number)
      visibility_timeout        = optional(number)
      max_receive_count         = optional(number)
    }))
  }))

  default     = []
  description = "List of SQS queues to create"
}
