## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.9 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 4.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | 4.67.0 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| aws_sqs_queue.dead_letter_queues | resource |
| aws_sqs_queue.queues | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_name_prefix"></a> [name\_prefix](#input\_name\_prefix) | Prefix to be used for all resource names | `string` | n/a | yes |
| <a name="input_queues"></a> [queues](#input\_queues) | List of SQS queues to create | <pre>list(object({<br/>    name       = string<br/>    fifo_queue  = optional(bool, false)<br/>    enable_dlq = optional(bool, false)<br/><br/>    # Optional queue settings<br/>    delay_seconds             = optional(number)<br/>    max_message_size          = optional(number)<br/>    message_retention_seconds = optional(number)<br/>    receive_wait_time_seconds = optional(number)<br/>    visibility_timeout        = optional(number)<br/><br/>    # FIFO specific settings<br/>    # deduplication_scope         = optional(string)<br/>    # fifo_throughput_limit       = optional(string)<br/>    content_based_deduplication = optional(bool)<br/><br/>    # DLQ specific settings<br/>    dlq_settings = optional(object({<br/>      delay_seconds             = optional(number)<br/>      max_message_size          = optional(number)<br/>      message_retention_seconds = optional(number)<br/>      receive_wait_time_seconds = optional(number)<br/>      visibility_timeout        = optional(number)<br/>      max_receive_count         = optional(number)<br/>    }))<br/>  }))</pre> | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | Tags to be applied to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_dlq_arns"></a> [dlq\_arns](#output\_dlq\_arns) | Map of DLQ names to their ARNs |
| <a name="output_dlq_urls"></a> [dlq\_urls](#output\_dlq\_urls) | Map of DLQ names to their URLs |
| <a name="output_queue_arns"></a> [queue\_arns](#output\_queue\_arns) | Map of queue names to their ARNs |
| <a name="output_queue_urls"></a> [queue\_urls](#output\_queue\_urls) | Map of queue names to their URLs |

## Module Summary

This AWS SQS module provides a standardized approach to creating and managing Amazon Simple Queue Service (SQS) queues. It simplifies the process of creating both standard and FIFO (First-In-First-Out) queues with optional dead-letter queues (DLQs) for handling message failures. The module implements best practices for queue configuration, including optimized performance settings, cost considerations, and security defaults.

## Features

- Creates both standard and FIFO SQS queues with consistent naming
- Supports optional dead-letter queues (DLQs) for handling failed message processing
- Configures redrive policies to automatically move failed messages to DLQs
- Applies AWS-recommended settings for queue parameters with sensible defaults
- Enables server-side encryption by default for enhanced security
- Optimizes queue parameters for cost-efficiency and performance
- Supports content-based deduplication for FIFO queues
- Provides consistent tagging across all resources
- Sets appropriate retention periods for both primary queues and DLQs
- Enforces long polling to reduce API costs and improve efficiency
- Includes upper bound validation for queue parameters to prevent configuration errors
- Outputs queue URLs and ARNs for easy integration with other AWS services

## Usage Examples

### Example 1:

This example creates simple standard and FIFO queues with default settings:

```hcl
module "basic_queues" {
  source = "sparrow-tofu-modules/components/aws/sqs"

  name_prefix = "myapp-dev"

  queues = [
    {
      name = "notifications"
      # Using default settings for a standard queue
    },
    {
      name       = "orders"
      fifo_queue = true
      # Enable content-based deduplication for the FIFO queue
      content_based_deduplication = true
    },
    {
      name       = "events"
      enable_dlq = true
      # Include a dead-letter queue with default settings
    }
  ]

  tags = {
    Environment = "development"
    Project     = "MyApplication"
    ManagedBy   = "Terraform"
  }
}

# Access queue URLs in outputs
output "notification_queue_url" {
  value = module.basic_queues.queue_urls["notifications"]
}

output "orders_queue_url" {
  value = module.basic_queues.queue_urls["orders"]
}

output "events_dlq_url" {
  value = module.basic_queues.dlq_urls["events"]
}
```

### Example 2:

This example demonstrates customized queues with specific settings for message handling, custom DLQ parameters, and advanced configurations:

```hcl
module "advanced_queues" {
  source = "sparrow-tofu-modules/components/aws/sqs"

  name_prefix = "production-app"

  queues = [
    # High-throughput processing queue with custom settings
    {
      name                      = "transactions"
      delay_seconds             = 5
      max_message_size          = 131072  # 128 KB per message
      message_retention_seconds = 172800  # 2 days retention
      visibility_timeout        = 180     # 3 minutes visibility timeout
      receive_wait_time_seconds = 20      # Long polling

      # Include DLQ with custom settings
      enable_dlq = true
      dlq_settings = {
        max_receive_count         = 3     # Maximum attempts before sending to DLQ
        message_retention_seconds = 604800 # 7 days retention in DLQ
        visibility_timeout        = 300    # 5 minutes visibility timeout in DLQ
      }
    },

    # Time-critical FIFO queue for ordered processing
    {
      name                      = "order-processing"
      fifo_queue                = true
      content_based_deduplication = true
      visibility_timeout        = 120     # 2 minute timeout
      message_retention_seconds = 86400   # 1 day retention

      # Include DLQ with stricter settings
      enable_dlq = true
      dlq_settings = {
        max_receive_count = 2     # Only 2 attempts before failing
        visibility_timeout = 300  # 5 minutes visibility in DLQ
      }
    },

    # Temporary storage queue with extended retention
    {
      name                      = "audit-logs"
      message_retention_seconds = 1209600 # Maximum 14 days retention
      max_message_size          = 262144  # Full 256 KB size

      # No DLQ needed as these are just logs
      enable_dlq = false
    },

    # Delayed processing queue
    {
      name                      = "scheduled-tasks"
      delay_seconds             = 300     # 5 minute delay before messages become available
      visibility_timeout        = 600     # 10 minute processing window
      enable_dlq                = true
      dlq_settings = {
        max_receive_count = 1     # Only one attempt for scheduled tasks
      }
    }
  ]

  tags = {
    Environment = "production"
    Department  = "engineering"
    CostCenter  = "platform-12345"
    Service     = "messaging"
    ManagedBy   = "terraform"
  }
}

# Output for documentation and reference
output "all_queue_urls" {
  value = module.advanced_queues.queue_urls
  description = "URLs of all created queues"
}

output "all_dlq_urls" {
  value = module.advanced_queues.dlq_urls
  description = "URLs of all created dead-letter queues"
}
```
