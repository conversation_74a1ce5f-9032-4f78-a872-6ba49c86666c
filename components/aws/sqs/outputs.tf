output "queue_urls" {
  description = "Map of queue names to their URLs"
  value = {
    for name, queue in aws_sqs_queue.queues : name => queue.url
  }
}

output "queue_arns" {
  description = "Map of queue names to their ARNs"
  value = {
    for name, queue in aws_sqs_queue.queues : name => queue.arn
  }
}

output "dlq_urls" {
  description = "Map of DLQ names to their URLs"
  value = {
    for name, queue in aws_sqs_queue.dead_letter_queues : name => queue.url
  }
}

output "dlq_arns" {
  description = "Map of DLQ names to their ARNs"
  value = {
    for name, queue in aws_sqs_queue.dead_letter_queues : name => queue.arn
  }
}
