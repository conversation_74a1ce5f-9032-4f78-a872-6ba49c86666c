locals {
  queue_map = {
    for idx, queue in var.queues : queue.name => queue
  }

  default_queue_settings = {
    delay_seconds             = 0
    max_message_size          = 262144 # 256 KB - reduced from 256KB to optimize costs
    message_retention_seconds = 86400  # 1 day - reduced from 4 days since most messages are processed within a day
    receive_wait_time_seconds = 20     # Enable long polling to reduce API calls and costs
    visibility_timeout        = 60     # 1 minute - balance between retries and quick failure detection
    deduplication_scope       = null

    fifo_queue            = false
    fifo_throughput_limit = null
  }
}

resource "aws_sqs_queue" "dead_letter_queues" {
  for_each = { for queue in local.queue_map : queue.name => queue if queue.enable_dlq == true }

  name = each.value.fifo_queue ? "${var.name_prefix}-${each.value.name}-dlq.fifo" : "${var.name_prefix}-${each.value.name}-dlq"

  delay_seconds               = min(try(each.value.dlq_settings.delay_seconds, local.default_queue_settings.delay_seconds), 900)
  max_message_size            = min(try(each.value.dlq_settings.max_message_size, local.default_queue_settings.max_message_size), 262144)
  message_retention_seconds   = min(try(each.value.dlq_settings.message_retention_seconds, 1209600), 1209600) # 14 days for DLQ
  receive_wait_time_seconds   = min(try(each.value.dlq_settings.receive_wait_time_seconds, local.default_queue_settings.receive_wait_time_seconds), 20)
  visibility_timeout_seconds  = min(try(each.value.dlq_settings.visibility_timeout, local.default_queue_settings.visibility_timeout), 43200)
  fifo_queue                  = try(each.value.fifo_queue, local.default_queue_settings.fifo_queue)
  content_based_deduplication = each.value.fifo_queue ? try(each.value.content_based_deduplication, false) : null


  sqs_managed_sse_enabled = true

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${each.value.name}-dlq"
  })
}


resource "aws_sqs_queue" "queues" {
  for_each = local.queue_map

  name = each.value.fifo_queue ? "${var.name_prefix}-${each.value.name}-queue.fifo" : "${var.name_prefix}-${each.value.name}-queue"

  delay_seconds              = min(coalesce(each.value.delay_seconds, local.default_queue_settings.delay_seconds), 900)
  max_message_size           = min(coalesce(each.value.max_message_size, local.default_queue_settings.max_message_size), 262144)
  message_retention_seconds  = min(coalesce(each.value.message_retention_seconds, local.default_queue_settings.message_retention_seconds), 1209600)
  receive_wait_time_seconds  = min(coalesce(each.value.receive_wait_time_seconds, local.default_queue_settings.receive_wait_time_seconds), 20)
  visibility_timeout_seconds = min(coalesce(each.value.visibility_timeout, local.default_queue_settings.visibility_timeout), 43200)

  fifo_queue                  = try(each.value.fifo_queue, local.default_queue_settings.fifo_queue)
  content_based_deduplication = each.value.fifo_queue ? try(each.value.content_based_deduplication, false) : null

  # Redrive Policy to connect to a Dead-Letter Queue
  redrive_policy = each.value.enable_dlq != false ? jsonencode({
    deadLetterTargetArn = aws_sqs_queue.dead_letter_queues[each.key].arn
    maxReceiveCount     = try(each.value.dlq_settings.max_receive_count, 5)
  }) : null

  # Enable server-side encryption by default
  sqs_managed_sse_enabled = true

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${each.value.name}-${each.value.fifo_queue ? "fifo-queue" : "standard-queue"}"
  })
}
