# SNS Module Usage Guide

This guide provides comprehensive examples and best practices for using the SNS module in your Terraform configurations.

## Quick Start

### 1. Simple Topic (No Prefix)

```hcl
module "sns" {
  source = "sparrow-tofu-modules/components/aws/sns"

  topics = [
    {
      name = "user-notifications"
      subscriptions = [
        {
          protocol = "email"
          endpoint = "<EMAIL>"
        }
      ]
    }
  ]

  tags = {
    Environment = "development"
    Project     = "myapp"
  }
}
```

### 2. Organized Topics with Prefix

```hcl
module "sns" {
  source = "sparrow-tofu-modules/components/aws/sns"

  name_prefix = "myapp-prod"  # Optional: adds prefix to all topic names

  topics = [
    {
      name = "alerts"  # becomes: myapp-prod-alerts-topic
      subscriptions = [
        {
          protocol = "email"
          endpoint = "<EMAIL>"
        }
      ]
    }
  ]

  tags = {
    Environment = "production"
    Project     = "myapp"
  }
}
```

## Advanced Examples

### 1. FIFO Topics with SQS Integration

```hcl
module "sns_fifo" {
  source = "sparrow-tofu-modules/components/aws/sns"

  name_prefix = "order-processing"

  topics = [
    {
      name                        = "orders"
      fifo_topic                  = true
      content_based_deduplication = true
      
      subscriptions = [
        {
          protocol = "sqs"
          endpoint = "arn:aws:sqs:us-west-2:123456789012:order-queue.fifo"
          raw_message_delivery = true
        }
      ]
    }
  ]

  tags = {
    Environment = "production"
    Service     = "order-processing"
  }
}
```

### 2. Multiple Topics with Different Configurations

```hcl
module "sns_multi" {
  source = "sparrow-tofu-modules/components/aws/sns"

  name_prefix = "myapp"

  topics = [
    # Standard topic for general notifications
    {
      name = "notifications"
      kms_master_key_id = "alias/sns-encryption-key"
      tracing_config = "Active"
      
      subscriptions = [
        {
          protocol = "email"
          endpoint = "<EMAIL>"
        },
        {
          protocol = "sms"
          endpoint = "+1234567890"
        }
      ]
    },
    
    # FIFO topic for ordered processing
    {
      name                        = "events"
      fifo_topic                  = true
      content_based_deduplication = true
      
      subscriptions = [
        {
          protocol = "sqs"
          endpoint = "arn:aws:sqs:us-west-2:123456789012:event-queue.fifo"
        }
      ]
    },
    
    # Topic with Lambda subscription
    {
      name = "webhooks"
      
      subscriptions = [
        {
          protocol = "lambda"
          endpoint = "arn:aws:lambda:us-west-2:123456789012:function:webhook-processor"
        }
      ]
    }
  ]

  tags = {
    Environment = "production"
    Project     = "myapp"
    ManagedBy   = "terraform"
  }
}
```

### 3. Topics with Filter Policies

```hcl
module "sns_filtered" {
  source = "sparrow-tofu-modules/components/aws/sns"

  name_prefix = "ecommerce"

  topics = [
    {
      name = "order-events"
      
      subscriptions = [
        # High-priority orders
        {
          protocol = "sqs"
          endpoint = "arn:aws:sqs:us-west-2:123456789012:high-priority-orders"
          filter_policy = jsonencode({
            priority = ["high"]
            amount = [{"numeric": [">", 1000]}]
          })
        },
        
        # All orders for analytics
        {
          protocol = "sqs"
          endpoint = "arn:aws:sqs:us-west-2:123456789012:analytics-queue"
          raw_message_delivery = true
        }
      ]
    }
  ]

  tags = {
    Environment = "production"
    Service     = "ecommerce"
  }
}
```

## Best Practices

### 1. Naming Best Practices

```hcl
# Good naming examples without prefix
module "sns_good_naming" {
  source = "sparrow-tofu-modules/components/aws/sns"

  topics = [
    # Descriptive and clear purpose
    { name = "user-registration-notifications" },
    { name = "payment-processing-alerts" },
    { name = "system-health-monitoring" },
    { name = "order-status-updates" },

    # Include environment when not using prefix
    { name = "prod-critical-alerts" },
    { name = "staging-test-notifications" }
  ]
}

# Good naming examples with prefix
module "sns_good_naming_with_prefix" {
  source = "sparrow-tofu-modules/components/aws/sns"
  
  name_prefix = "myapp-prod"
  
  topics = [
    # Short, clear names (prefix provides context)
    { name = "alerts" },      # becomes: myapp-prod-alerts-topic
    { name = "notifications" }, # becomes: myapp-prod-notifications-topic
    { name = "events" }       # becomes: myapp-prod-events-topic
  ]
}
```

### 2. Security Best Practices

```hcl
module "sns_secure" {
  source = "sparrow-tofu-modules/components/aws/sns"

  name_prefix = "secure-app"

  topics = [
    {
      name = "sensitive-data"
      
      # Use custom KMS key for encryption
      kms_master_key_id = "arn:aws:kms:us-west-2:123456789012:key/12345678-1234-1234-1234-123456789012"
      
      # Enable X-Ray tracing for monitoring
      tracing_config = "Active"
      
      subscriptions = [
        {
          protocol = "sqs"
          endpoint = "arn:aws:sqs:us-west-2:123456789012:secure-queue"
          # Enable raw message delivery for better performance
          raw_message_delivery = true
        }
      ]
    }
  ]

  tags = {
    Environment = "production"
    DataClass   = "sensitive"
    Compliance  = "required"
  }
}
```

### 3. FIFO Topic Best Practices

```hcl
module "sns_fifo_best_practices" {
  source = "sparrow-tofu-modules/components/aws/sns"

  name_prefix = "order-system"

  topics = [
    {
      name = "order-events"
      fifo_topic = true
      
      # Enable content-based deduplication for automatic deduplication
      content_based_deduplication = true
      
      # FIFO topics only support SQS subscriptions
      subscriptions = [
        {
          protocol = "sqs"
          endpoint = "arn:aws:sqs:us-west-2:123456789012:order-processing.fifo"
          raw_message_delivery = true
        }
      ]
    }
  ]

  tags = {
    Environment = "production"
    MessageOrdering = "required"
  }
}
```

## Output Usage

### Accessing Topic Information

```hcl
# Get topic ARNs for use in other resources
output "notification_topic_arn" {
  value = module.sns.topic_arns["notifications"]
}

# Get all topic details
output "all_topics" {
  value = module.sns.topic_details
}

# Get application configuration
output "app_config" {
  value = module.sns.topic_configuration
}
```

### Integration with Other Resources

```hcl
# Use topic ARN in CloudWatch alarm
resource "aws_cloudwatch_metric_alarm" "high_error_rate" {
  alarm_name          = "high-error-rate"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "ErrorRate"
  namespace           = "AWS/ApplicationELB"
  period              = "300"
  statistic           = "Average"
  threshold           = "5"
  alarm_description   = "This metric monitors application error rate"
  
  alarm_actions = [module.sns.topic_arns["alerts"]]
}

# Use topic ARN in Lambda function environment
resource "aws_lambda_function" "processor" {
  filename         = "processor.zip"
  function_name    = "event-processor"
  role            = aws_iam_role.lambda_role.arn
  handler         = "index.handler"
  runtime         = "python3.9"

  environment {
    variables = {
      SNS_TOPIC_ARN = module.sns.topic_arns["events"]
    }
  }
}
```

## Migration Guide

### From Single Topic to Multi-Topic

```hcl
# Old single-topic usage
module "sns_old" {
  source = "sparrow-tofu-modules/components/aws/sns"
  
  name_prefix = "myapp"
  
  topics = [
    {
      name = "notifications"
      subscriptions = [
        {
          protocol = "email"
          endpoint = "<EMAIL>"
        }
      ]
    }
  ]
}

# New multi-topic usage
module "sns_new" {
  source = "sparrow-tofu-modules/components/aws/sns"
  
  name_prefix = "myapp"
  
  topics = [
    {
      name = "notifications"  # Same topic name for compatibility
      subscriptions = [
        {
          protocol = "email"
          endpoint = "<EMAIL>"
        }
      ]
    },
    {
      name = "alerts"  # Additional topic
      subscriptions = [
        {
          protocol = "sms"
          endpoint = "+1234567890"
        }
      ]
    }
  ]
}
```

## Support

For issues with the SNS module:
1. Check the validation errors in the plan output
2. Verify your AWS permissions for SNS operations
3. Review the AWS SNS documentation for feature limitations
4. Check the module source code for advanced customization options
