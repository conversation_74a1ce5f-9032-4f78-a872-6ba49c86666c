## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.9 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.0 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_sns_topic.topics](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/sns_topic) | resource |
| [aws_sns_topic_subscription.subscriptions](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/sns_topic_subscription) | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_name_prefix"></a> [name\_prefix](#input\_name\_prefix) | Prefix to be used for all resource names | `string` | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | Tags to be applied to all resources | `map(string)` | n/a | yes |
| <a name="input_topics"></a> [topics](#input\_topics) | List of SNS topics to create | <pre>list(object({<br/>    name       = string<br/>    fifo_topic = optional(bool, false)<br/><br/>    # Optional topic settings<br/>    content_based_deduplication = optional(bool)<br/>    signature_version           = optional(string)<br/><br/>    tracing_config    = optional(string)<br/>    kms_master_key_id = optional(string)<br/><br/>    # Subscriptions<br/>    subscriptions = optional(list(object({<br/>      protocol             = string<br/>      endpoint             = string<br/>      filter_policy        = optional(string)<br/>      raw_message_delivery = optional(bool)<br/>    })))<br/>  }))</pre> | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_topic_arns"></a> [topic\_arns](#output\_topic\_arns) | Map of topic names to their ARNs |
| <a name="output_topic_subscriptions"></a> [topic\_subscriptions](#output\_topic\_subscriptions) | Map of subscription IDs to their ARNs |

## Module Summary

This SNS (Simple Notification Service) module provides a standardized way to create and manage AWS SNS topics and their subscriptions. It simplifies the process of setting up notification infrastructure by offering a consistent interface for defining topics with sensible defaults while allowing full customization of advanced features.

## Features

- Creates multiple SNS topics with a single module call
- Supports both standard and FIFO (First-In-First-Out) topics
- Configures topic subscriptions of various protocols (email, SMS, SQS, Lambda, etc.)
- Enables server-side encryption by default using AWS managed KMS keys
- Supports content-based deduplication for FIFO topics
- Allows filter policies for selective message delivery to subscriptions
- Supports raw message delivery for compatible protocols
- Enforces unique topic names to prevent conflicts
- Implements consistent naming convention with a customizable prefix
- Provides standardized tagging across all resources
- Configurable X-Ray tracing for monitoring message delivery
- Includes validation to ensure FIFO topic subscriptions use supported protocols

## Usage Examples

### Example 1:

This example creates simple notification topics for monitoring and alerts:

```hcl
module "basic_sns" {
  source = "sparrow-tofu-modules/components/aws/sns"

  name_prefix = "myapp-dev"

  topics = [
    {
      name = "system-alerts"
      # Using default settings (standard topic with default encryption)

      subscriptions = [
        {
          protocol = "email"
          endpoint = "<EMAIL>"
        },
        {
          protocol = "sms"
          endpoint = "+12065550100"
        }
      ]
    },
    {
      name = "monitoring-notifications"

      subscriptions = [
        {
          protocol = "email"
          endpoint = "<EMAIL>"
        }
      ]
    }
  ]

  tags = {
    Environment = "development"
    Project     = "MyApplication"
    ManagedBy   = "Terraform"
  }
}

# Access the topic ARNs for reference in other resources
output "alert_topic_arn" {
  value = module.basic_sns.topic_arns["system-alerts"]
}
```

### Example 2:

This example demonstrates advanced module usage with FIFO topics, filter policies, and multiple subscription types:

```hcl
module "advanced_sns" {
  source = "sparrow-tofu-modules/components/aws/sns"

  name_prefix = "production-app"

  topics = [
    # Standard topic with custom KMS encryption and different subscription protocols
    {
      name = "platform-alerts"
      kms_master_key_id = "alias/custom-sns-key"  # Using existing KMS key
      tracing_config = "Active"  # Enable X-Ray tracing
      signature_version = "1"    # Custom signature version

      subscriptions = [
        {
          # Lambda subscription with filtering and raw delivery
          protocol = "lambda"
          endpoint = "arn:aws:lambda:us-west-2:123456789012:function:process-alerts"
          filter_policy = jsonencode({
            severity = ["CRITICAL", "ERROR"]
            resource = ["database", "api-gateway"]
          })
          raw_message_delivery = true
        },
        {
          # Webhook subscription with filtering
          protocol = "https"
          endpoint = "https://alerts.example.com/webhook"
          filter_policy = jsonencode({
            severity = ["CRITICAL"]
            region = ["us-west-2", "us-east-1"]
          })
        },
        {
          # Email subscription with JSON format
          protocol = "email-json"
          endpoint = "<EMAIL>"
        }
      ]
    },

    # FIFO topic for ordered event processing
    {
      name = "order-events"
      fifo_topic = true
      content_based_deduplication = true

      subscriptions = [
        {
          # SQS subscription (only supported protocol for FIFO topics)
          protocol = "sqs"
          endpoint = "arn:aws:sqs:us-west-2:123456789012:order-processing-queue.fifo"
          raw_message_delivery = true
        }
      ]
    },

    # Topic with minimal configuration - just for internal services
    {
      name = "internal-events"

      subscriptions = [
        {
          # SQS subscription
          protocol = "sqs"
          endpoint = "arn:aws:sqs:us-west-2:123456789012:internal-events-queue"
        }
      ]
    }
  ]

  tags = {
    Environment = "production"
    Department  = "Engineering"
    Service     = "Notifications"
    CostCenter  = "12345"
    ManagedBy   = "Terraform"
  }
}

# Output the topic ARNs for reference in other resources
output "all_topic_arns" {
  value = module.advanced_sns.topic_arns
}

output "fifo_topic_arn" {
  value = module.advanced_sns.topic_arns["order-events"]
}
```
