# sparrow-tofu-modules/components/aws/sns/outputs.tf

output "topic_details" {
  description = "Details of all created SNS topics."
  value = {
    for k, topic in aws_sns_topic.topics : k => {
      id                          = topic.id
      arn                         = topic.arn
      name                        = topic.name
      display_name                = topic.display_name
      policy                      = topic.policy
      delivery_policy             = topic.delivery_policy
      application_success_feedback_role_arn    = topic.application_success_feedback_role_arn
      application_success_feedback_sample_rate = topic.application_success_feedback_sample_rate
      application_failure_feedback_role_arn    = topic.application_failure_feedback_role_arn
      http_success_feedback_role_arn           = topic.http_success_feedback_role_arn
      http_success_feedback_sample_rate        = topic.http_success_feedback_sample_rate
      http_failure_feedback_role_arn           = topic.http_failure_feedback_role_arn
      kms_master_key_id                        = topic.kms_master_key_id
      fifo_topic                               = topic.fifo_topic
      content_based_deduplication              = topic.content_based_deduplication
      tags                                     = topic.tags
    }
  }
}

output "topic_names" {
  description = "Map of topic keys to their full names (including prefix)."
  value       = { for k, topic in aws_sns_topic.topics : k => topic.name }
}

output "topic_arns" {
  description = "Map of topic keys to their ARNs."
  value       = { for k, topic in aws_sns_topic.topics : k => topic.arn }
}

output "topic_ids" {
  description = "Map of topic keys to their IDs."
  value       = { for k, topic in aws_sns_topic.topics : k => topic.id }
}

output "subscription_details" {
  description = "Details of all created SNS topic subscriptions."
  value = {
    for k, sub in aws_sns_topic_subscription.subscriptions : k => {
      id                   = sub.id
      arn                  = sub.arn
      topic_arn           = sub.topic_arn
      protocol            = sub.protocol
      endpoint            = sub.endpoint
      filter_policy       = sub.filter_policy
      raw_message_delivery = sub.raw_message_delivery
      confirmation_timeout_in_minutes = sub.confirmation_timeout_in_minutes
      endpoint_auto_confirms          = sub.endpoint_auto_confirms
      pending_confirmation            = sub.pending_confirmation
    }
  }
}

output "subscription_arns" {
  description = "Map of subscription keys to their ARNs."
  value       = { for k, sub in aws_sns_topic_subscription.subscriptions : k => sub.arn }
}

# Application configuration output
output "topic_configuration" {
  description = "Configuration values for applications using these topics."
  value = {
    topic_names = { for k, topic in aws_sns_topic.topics : k => topic.name }
    topic_arns  = { for k, topic in aws_sns_topic.topics : k => topic.arn }
    topic_urls = {
      for k, topic in aws_sns_topic.topics : k => "https://sns.${data.aws_region.current.name}.amazonaws.com/${topic.arn}"
    }
  }
}

# Convenience outputs for common use cases
output "fifo_topics" {
  description = "Map of FIFO topic keys to their details."
  value = {
    for k, topic in aws_sns_topic.topics : k => {
      name = topic.name
      arn  = topic.arn
    } if topic.fifo_topic
  }
}

output "standard_topics" {
  description = "Map of standard topic keys to their details."
  value = {
    for k, topic in aws_sns_topic.topics : k => {
      name = topic.name
      arn  = topic.arn
    } if !topic.fifo_topic
  }
}

# Data source for current region
data "aws_region" "current" {}
