variable "name_prefix" {
  description = "A prefix for all resource names to ensure uniqueness and grouping. If not provided, only the topic name will be used."
  type        = string
  default     = ""
  nullable    = false
}

variable "tags" {
  description = "A map of tags to assign to all created resources."
  type        = map(string)
  default     = {}
  nullable    = false
}

variable "topics" {
  type = list(object({
    name       = string
    fifo_topic = optional(bool, false)

    # Optional topic settings
    content_based_deduplication = optional(bool)
    signature_version           = optional(string)

    tracing_config    = optional(string)
    kms_master_key_id = optional(string)

    # Subscriptions
    subscriptions = optional(list(object({
      protocol             = string
      endpoint             = string
      filter_policy        = optional(string)
      raw_message_delivery = optional(bool)
    })))
  }))
  description = "List of SNS topics to create"

  validation {
    condition     = length(var.topics) == length(distinct([for topic in var.topics : topic.name]))
    error_message = "Topic names must be unique within the list."
  }

  validation {
    condition = alltrue([
      for topic in var.topics : length(topic.name) > 0 && length(topic.name) <= 256
    ])
    error_message = "Topic name must be between 1 and 256 characters."
  }

  validation {
    condition = alltrue([
      for topic in var.topics : topic.subscriptions == null || alltrue([
        for sub in topic.subscriptions : contains(["http", "https", "email", "email-json", "sms", "sqs", "application", "lambda", "firehose"], sub.protocol)
      ])
    ])
    error_message = "Subscription protocol must be one of: http, https, email, email-json, sms, sqs, application, lambda, firehose."
  }

  validation {
    condition = alltrue([
      for topic in var.topics : !topic.fifo_topic || (topic.subscriptions == null || alltrue([
        for sub in topic.subscriptions : contains(["sqs"], sub.protocol)
      ]))
    ])
    error_message = "FIFO topics only support SQS subscriptions."
  }
}
