variable "name_prefix" {
  type        = string
  description = "Prefix to be used for all resource names"
}

variable "tags" {
  type        = map(string)
  description = "Tags to be applied to all resources"
}

variable "topics" {
  type = list(object({
    name       = string
    fifo_topic = optional(bool, false)

    # Optional topic settings
    content_based_deduplication = optional(bool)
    signature_version           = optional(string)

    tracing_config    = optional(string)
    kms_master_key_id = optional(string)

    # Subscriptions
    subscriptions = optional(list(object({
      protocol             = string
      endpoint             = string
      filter_policy        = optional(string)
      raw_message_delivery = optional(bool)
    })))
  }))
  description = "List of SNS topics to create"

  validation {
    condition     = length(var.topics) == length(distinct([for topic in var.topics : topic.name]))
    error_message = "Topic names must be unique within the list."
  }
}
