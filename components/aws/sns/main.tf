# ------------------------------------------------------------------------------
# SNS Terraform Module
# This module creates SNS topics with comprehensive configurations and security best practices
# ------------------------------------------------------------------------------

terraform {
  required_providers {
    aws = {
      source = "hashicorp/aws"
    }
  }
}

# ------------------------------------------------------------------------------
# Local Values
# ------------------------------------------------------------------------------

locals {
  topic_map = {
    for idx, topic in var.topics : topic.name => topic
  }

  default_topic_settings = {
    fifo_topic                  = false
    content_based_deduplication = false
    signature_version           = "2"
    tracing_config              = "PassThrough"
    kms_master_key_id           = "alias/aws/sns"
  }
}

resource "aws_sns_topic" "topics" {
  for_each = local.topic_map

  name = each.value.fifo_topic ? (
    var.name_prefix != "" ? "${var.name_prefix}-${each.value.name}-topic.fifo" : "${each.value.name}-topic.fifo"
  ) : (
    var.name_prefix != "" ? "${var.name_prefix}-${each.value.name}-topic" : "${each.value.name}-topic"
  )

  fifo_topic                  = coalesce(each.value.fifo_topic, local.default_topic_settings.fifo_topic)
  content_based_deduplication = each.value.fifo_topic ? coalesce(each.value.content_based_deduplication, local.default_topic_settings.content_based_deduplication) : null
  signature_version           = coalesce(each.value.signature_version, local.default_topic_settings.signature_version)
  tracing_config              = coalesce(each.value.tracing_config, local.default_topic_settings.tracing_config)

  # Enable server-side encryption by default Ignoring Trivy Scan - since we are using default KMS key.
  # trivy:ignore:AVD-AWS-0136
  kms_master_key_id = coalesce(each.value.kms_master_key_id, local.default_topic_settings.kms_master_key_id)

  tags = merge(var.tags, {
    Name = var.name_prefix != "" ? "${var.name_prefix}-${each.value.name}-topic" : "${each.value.name}-topic"
  })
}

# Create topic subscriptions if specified
resource "aws_sns_topic_subscription" "subscriptions" {
  for_each = {
    for sub in flatten([
      for topic_name, topic in local.topic_map : [
        for subscription in coalesce(topic.subscriptions, []) : {
          topic_name   = topic_name
          subscription = subscription
        } if !topic.fifo_topic || (topic.fifo_topic && subscription.protocol == "sqs")
      ]
    ]) : "${sub.topic_name}-${sub.subscription.protocol}-${sub.subscription.endpoint}" => sub
  }

  topic_arn = aws_sns_topic.topics[each.value.topic_name].arn
  protocol  = each.value.subscription.protocol
  endpoint  = each.value.subscription.endpoint

  filter_policy        = try(each.value.subscription.filter_policy, null)
  raw_message_delivery = coalesce(each.value.subscription.raw_message_delivery, false)
}
