variable "name_prefix" {
  type        = string
  description = "Prefix to be used for all resource names"
}

variable "tags" {
  type        = map(string)
  description = "Tags to be applied to all resources"
}

variable "alarms" {
  type = list(object({
    name                      = string
    description               = optional(string)
    resource                  = string           # AWS service resource type (e.g., ec2, rds, lambda)
    resource_id               = optional(string) # The ID of the resource (e.g., instance ID, function name)
    metric_name               = string
    threshold                 = number
    dimensions                = optional(map(string)) # Optional custom dimensions (overrides resource_id if provided)
    period                    = optional(number)
    statistic                 = optional(string)
    comparison_operator       = optional(string)
    evaluation_periods        = optional(number)
    datapoints_to_alarm       = optional(number)
    treat_missing_data        = optional(string)
    alarm_actions             = optional(list(string))
    ok_actions                = optional(list(string))
    insufficient_data_actions = optional(list(string))
  }))
  description = "List of CloudWatch alarms to create"

  validation {
    condition     = length(distinct([for alarm in var.alarms : alarm.name])) == length(var.alarms)
    error_message = "Alarm names must be unique across all alarms."
  }
}

# These are there for fallback. If there is a service that hasn't asked for an Alarm, we will use Topics owned by DevOps Team.
variable "default_alarm_actions" {
  type        = list(string)
  description = "Default list of ARNs (typically SNS topics) to execute when alarm state is reached"
  default     = []
}

variable "default_ok_actions" {
  type        = list(string)
  description = "Default list of ARNs (typically SNS topics) to execute when alarm state returns to normal"
  default     = []
}

variable "default_insufficient_data_actions" {
  type        = list(string)
  description = "Default list of ARNs (typically SNS topics) to execute when alarm has insufficient data"
  default     = []
}
