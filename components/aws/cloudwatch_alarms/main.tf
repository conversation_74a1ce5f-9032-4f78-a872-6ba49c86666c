locals {
  alarm_map = {
    for idx, alarm in var.alarms : alarm.name => alarm
  }

  default_alarm_settings = {
    comparison_operator       = "GreaterThanOrEqualToThreshold"
    evaluation_periods        = 2 # Requires 2 evaluation periods to avoid false alarms
    treat_missing_data        = "notBreaching"
    datapoints_to_alarm       = 2 # Requires 2 data points to trigger alarm (more stable)
    description               = null
    alarm_actions             = coalesce(var.default_alarm_actions, [])
    ok_actions                = coalesce(var.default_ok_actions, [])
    insufficient_data_actions = coalesce(var.default_insufficient_data_actions, [])
    statistic                 = "Average"
    period                    = 300 # 5-minute periods for cost efficiency
    threshold                 = 1
    dimensions                = {}
  }

  # Standard namespaces for AWS services
  standard_namespaces = {
    ec2         = "AWS/EC2"
    rds         = "AWS/RDS"
    lambda      = "AWS/Lambda"
    sqs         = "AWS/SQS"
    sns         = "AWS/SNS"
    apigateway  = "AWS/ApiGateway"
    dynamodb    = "AWS/DynamoDB"
    elasticache = "AWS/ElastiCache"
    s3          = "AWS/S3"
    ecs         = "AWS/ECS"
    eks         = "AWS/EKS"
    elb         = "AWS/ELB"
    alb         = "AWS/ApplicationELB"
    nlb         = "AWS/NetworkELB"
    cloudfront  = "AWS/CloudFront"
    route53     = "AWS/Route53"
  }

  # Default dimension key for each resource type
  default_dimension_keys = {
    "ec2"           = "InstanceId"
    "rds"           = "DBClusterIdentifier"
    "opensearch"    = "DomainName"
    "elasticsearch" = "DomainName"
    "lambda"        = "FunctionName"
    "sqs"           = "QueueName"
    "sns"           = "TopicName"
    "apigateway"    = "ApiName"
    "dynamodb"      = "TableName"
    "elasticache"   = "CacheClusterId"
    "s3"            = "BucketName"
    "ecs"           = "ClusterName"
    "eks"           = "ClusterName"
    "elb"           = "LoadBalancerName"
    "alb"           = "LoadBalancerName"
    "nlb"           = "LoadBalancerName"
    "cloudfront"    = "DistributionId"
    "route53"       = "HostedZoneId"
  }

  # Process dimensions for each alarm
  processed_alarms = {
    for name, alarm in local.alarm_map : name => {
      name                      = alarm.name
      resource                  = alarm.resource
      metric_name               = alarm.metric_name
      threshold                 = alarm.threshold
      comparison_operator       = alarm.comparison_operator
      evaluation_periods        = alarm.evaluation_periods
      datapoints_to_alarm       = alarm.datapoints_to_alarm
      treat_missing_data        = alarm.treat_missing_data
      description               = alarm.description
      period                    = alarm.period
      statistic                 = alarm.statistic
      alarm_actions             = alarm.alarm_actions
      ok_actions                = alarm.ok_actions
      insufficient_data_actions = alarm.insufficient_data_actions

      # Process dimensions based on whether it's a simple resource_id or custom dimensions
      dimensions = (
        # If dimensions are provided, use them as-is
        alarm.dimensions != null ? alarm.dimensions : (
          # If resource_id is provided and the resource has a default dimension key, create a dimension map
          (alarm.resource_id != null && contains(keys(local.default_dimension_keys), alarm.resource)) ?
          { local.default_dimension_keys[alarm.resource] = alarm.resource_id } :
          {}
        )
      )
    }
  }
}

resource "aws_cloudwatch_metric_alarm" "this" {
  for_each = local.processed_alarms

  alarm_name  = "${var.name_prefix}-${each.value.name}-alarm"
  namespace   = lookup(local.standard_namespaces, each.value.resource, "AWS/${each.value.resource}")
  metric_name = each.value.metric_name

  # Required alarm properties
  comparison_operator = coalesce(each.value.comparison_operator, local.default_alarm_settings.comparison_operator)
  evaluation_periods  = coalesce(each.value.evaluation_periods, local.default_alarm_settings.evaluation_periods)
  threshold           = each.value.threshold != null ? each.value.threshold : local.default_alarm_settings.threshold
  period              = coalesce(each.value.period, local.default_alarm_settings.period)
  statistic           = coalesce(each.value.statistic, local.default_alarm_settings.statistic)

  # Optional alarm properties with defaults
  treat_missing_data  = coalesce(each.value.treat_missing_data, local.default_alarm_settings.treat_missing_data)
  datapoints_to_alarm = coalesce(each.value.datapoints_to_alarm, local.default_alarm_settings.evaluation_periods)

  # Use processed dimensions
  dimensions = each.value.dimensions

  alarm_description = coalesce(each.value.description, "Alarm for ${each.value.metric_name} in ${lookup(local.standard_namespaces, each.value.resource, "AWS/${each.value.resource}")}")

  # Actions
  alarm_actions             = coalesce(each.value.alarm_actions, local.default_alarm_settings.alarm_actions)
  ok_actions                = coalesce(each.value.ok_actions, local.default_alarm_settings.ok_actions)
  insufficient_data_actions = coalesce(each.value.insufficient_data_actions, local.default_alarm_settings.insufficient_data_actions)

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${each.value.name}-alarm"
  })
}
