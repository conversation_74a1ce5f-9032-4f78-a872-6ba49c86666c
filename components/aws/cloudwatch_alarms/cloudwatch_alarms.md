## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.0.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.0 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_cloudwatch_metric_alarm.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/cloudwatch_metric_alarm) | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_alarms"></a> [alarms](#input\_alarms) | List of CloudWatch alarms to create | <pre>list(object({<br/>    name                      = string<br/>    description               = optional(string)<br/>    resource                  = string           # AWS service resource type (e.g., ec2, rds, lambda)<br/>    resource_id               = optional(string) # The ID of the resource (e.g., instance ID, function name)<br/>    metric_name               = string<br/>    threshold                 = number<br/>    dimensions                = optional(map(string)) # Optional custom dimensions (overrides resource_id if provided)<br/>    period                    = optional(number)<br/>    statistic                 = optional(string)<br/>    comparison_operator       = optional(string)<br/>    evaluation_periods        = optional(number)<br/>    datapoints_to_alarm       = optional(number)<br/>    treat_missing_data        = optional(string)<br/>    alarm_actions             = optional(list(string))<br/>    ok_actions                = optional(list(string))<br/>    insufficient_data_actions = optional(list(string))<br/>  }))</pre> | `[]` | no |
| <a name="input_default_alarm_actions"></a> [default\_alarm\_actions](#input\_default\_alarm\_actions) | Default list of ARNs (typically SNS topics) to execute when alarm state is reached | `list(string)` | `[]` | no |
| <a name="input_default_insufficient_data_actions"></a> [default\_insufficient\_data\_actions](#input\_default\_insufficient\_data\_actions) | Default list of ARNs (typically SNS topics) to execute when alarm has insufficient data | `list(string)` | `[]` | no |
| <a name="input_default_ok_actions"></a> [default\_ok\_actions](#input\_default\_ok\_actions) | Default list of ARNs (typically SNS topics) to execute when alarm state returns to normal | `list(string)` | `[]` | no |
| <a name="input_name_prefix"></a> [name\_prefix](#input\_name\_prefix) | Prefix to be used for all resource names | `string` | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | Tags to be applied to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_alarm_arns"></a> [alarm\_arns](#output\_alarm\_arns) | Map of alarm names to their ARNs |
| <a name="output_alarm_ids"></a> [alarm\_ids](#output\_alarm\_ids) | Map of alarm names to their IDs |

## Module Summary

This module simplifies the creation and management of AWS CloudWatch metric alarms across multiple AWS services. It provides a standardized interface for defining alarms with sensible defaults while maintaining flexibility for customization. The module handles the complexity of CloudWatch namespace mapping and dimension configuration based on AWS service types.

## Features

- Simplifies alarm creation by automatically mapping AWS service types to appropriate CloudWatch namespaces
- Automatically configures default dimensions based on resource type (EC2, RDS, Lambda, etc.)
- Provides sensible defaults for alarm parameters while allowing full customization
- Supports custom dimensions for advanced monitoring scenarios
- Enforces unique alarm names to prevent conflicts
- Configurable notification actions for alarm state changes (ALARM, OK, INSUFFICIENT_DATA)
- Standardized naming convention with customizable prefix
- Consistent tagging strategy across all resources
- Outputs alarm ARNs and IDs for referencing in other resources

## Usage Examples

### Example 1:

This example shows a simple configuration to monitor basic metrics for common AWS resources:

```hcl
module "basic_alarms" {
  source = "sparrow-tofu-modules/components/aws/cloudwatch_alarms"

  name_prefix = "myapp-dev"

  # Set up a default SNS topic to receive all alarm notifications
  default_alarm_actions = ["arn:aws:sns:us-west-2:123456789012:my-alerts-topic"]

  # Define simple alarms with minimal configuration
  alarms = [
    # Monitor CPU utilization for an EC2 instance
    {
      name        = "high-cpu"
      resource    = "ec2"
      resource_id = "i-1234567890abcdef0"
      metric_name = "CPUUtilization"
      threshold   = 80  # Alarm when CPU is above 80%
    },

    # Monitor free storage space for an RDS instance
    {
      name        = "low-storage"
      resource    = "rds"
      resource_id = "my-database-cluster"
      metric_name = "FreeStorageSpace"
      threshold   = 10000000000  # 10GB in bytes
      comparison_operator = "LessThanOrEqualToThreshold"
    }
  ]

  tags = {
    Environment = "development"
    ManagedBy   = "terraform"
  }
}

# Output the created alarm ARNs for reference
output "alarm_arns" {
  value = module.basic_alarms.alarm_arns
}
```

### Example 2:

This example demonstrates advanced configuration options including custom dimensions, specific statistics, and different notification targets for different alarm states:

```hcl
# First, create SNS topics for different alert levels
module "sns_topics" {
  source = "terraform-aws-modules/sns/aws"

  names = ["critical-alerts", "warning-alerts", "info-alerts"]
}

module "advanced_alarms" {
  source = "sparrow-tofu-modules/components/aws/cloudwatch_alarms"

  name_prefix = "production-platform"

  alarms = [
    # Advanced Lambda monitoring with custom evaluation criteria
    {
      name                 = "lambda-errors"
      description          = "Monitors Lambda invocation errors with tight thresholds"
      resource             = "lambda"
      resource_id          = "payment-processor"
      metric_name          = "Errors"
      threshold            = 1
      period               = 60               # Check every minute
      evaluation_periods   = 3                # Must breach for 3 consecutive periods
      datapoints_to_alarm  = 2                # But only need 2 data points to trigger
      statistic            = "Sum"            # Count total errors, not average
      comparison_operator  = "GreaterThanThreshold"
      treat_missing_data   = "breaching"      # Missing data is considered a problem
      alarm_actions        = [module.sns_topics.topic_arns["critical-alerts"]]
    },

    # Custom metric with custom dimensions
    {
      name           = "api-latency"
      description    = "Monitors API Gateway latency for critical endpoints"
      resource       = "apigateway"
      metric_name    = "Latency"
      threshold      = 500                   # 500ms threshold
      period         = 300                   # 5 minute periods
      statistic      = "p90"                 # 90th percentile instead of average
      # Custom dimensions for specific endpoints
      dimensions     = {
        ApiName     = "customer-api",
        Stage       = "production",
        Resource    = "/transactions",
        Method      = "POST"
      }
      alarm_actions  = [module.sns_topics.topic_arns["warning-alerts"]]
      ok_actions     = [module.sns_topics.topic_arns["info-alerts"]]
    },

    # DynamoDB monitoring with detailed configuration
    {
      name                = "dynamodb-throttles"
      resource            = "dynamodb"
      resource_id         = "user-sessions-table"
      metric_name         = "ThrottledRequests"
      threshold           = 10
      period              = 60
      evaluation_periods  = 5
      datapoints_to_alarm = 4
      statistic           = "SampleCount"
      alarm_actions       = [
        module.sns_topics.topic_arns["critical-alerts"],
        "arn:aws:sns:us-west-2:123456789012:ops-escalation"  # Additional notification
      ]
      # Different action for insufficient data
      insufficient_data_actions = [module.sns_topics.topic_arns["warning-alerts"]]
    }
  ]

  # Common tags for all resources
  tags = {
    Environment  = "production"
    CostCenter   = "platform-engineering"
    ManagedBy    = "terraform"
    Service      = "monitoring"
    Owner        = "ops-team"
  }
}
