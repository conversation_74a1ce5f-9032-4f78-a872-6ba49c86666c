locals {
  default_iam_settings = {
    max_session_duration = 3600
    path                 = "/"
  }

  role_names_map = { for role in var.roles : role.name => role }
  role_policy_names_map = { for policy in flatten([
    for role in var.roles : [
      for custom_policy in(role.policies != null ? role.policies : []) : merge(custom_policy, { role_name = role.name })
    ]
    ]) : policy.name => policy
  }

  managed_policy_names_map = { for policy in flatten([
    for role in var.roles : [
      for managed_policy in(role.managed_policy_arns != null ? role.managed_policy_arns : []) : {
        role_name  = role.name
        policy_arn = managed_policy
      }
    ]
    ]) : split("/", policy.policy_arn)[length(split("/", policy.policy_arn)) - 1] => policy
  }
  standalone_policy_names_map = { for policy in var.standalone_policies : policy.name => policy }
}

# Create IAM roles
resource "aws_iam_role" "this" {
  for_each = local.role_names_map

  name                 = "${var.name_prefix}-${each.value.name}-role"
  description          = each.value.description
  assume_role_policy   = each.value.trust_relationship
  max_session_duration = coalesce(each.value.max_session_duration, local.default_iam_settings.max_session_duration)
  path                 = coalesce(each.value.path, local.default_iam_settings.path)

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${each.value.name}-role"
  })
}

# Create custom policies
resource "aws_iam_policy" "custom" {
  for_each = local.role_policy_names_map

  name        = "${var.name_prefix}-${each.value.name}-policy"
  description = each.value.description
  policy      = each.value.policy

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${each.value.name}-policy"
  })
}

# Attach custom policies to roles
resource "aws_iam_role_policy_attachment" "custom_policy_attachments" {
  for_each = local.role_policy_names_map

  role       = aws_iam_role.this[each.value.role_name].name
  policy_arn = aws_iam_policy.custom[each.value.name].arn
}

# Attach managed policies to roles
resource "aws_iam_role_policy_attachment" "managed_policy_attachments" {
  for_each = local.managed_policy_names_map

  role       = aws_iam_role.this[each.value.role_name].name
  policy_arn = each.value.policy_arn
}

# Create standalone policies
resource "aws_iam_policy" "standalone" {
  for_each = local.standalone_policy_names_map

  name        = "${var.name_prefix}-${each.value.name}-policy"
  description = each.value.description
  policy      = each.value.policy
  path        = each.value.path

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${each.value.name}-policy"
  })
}
