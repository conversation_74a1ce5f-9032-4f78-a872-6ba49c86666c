## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.0.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.0 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_iam_policy.custom](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_policy.standalone](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_policy) | resource |
| [aws_iam_role.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role) | resource |
| [aws_iam_role_policy_attachment.custom_policy_attachments](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |
| [aws_iam_role_policy_attachment.managed_policy_attachments](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role_policy_attachment) | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_name_prefix"></a> [name\_prefix](#input\_name\_prefix) | Prefix to be used for all resource names | `string` | n/a | yes |
| <a name="input_roles"></a> [roles](#input\_roles) | List of IAM roles to create with their policies | <pre>list(object({<br/>    name               = string<br/>    trust_relationship = string<br/>    description        = optional(string, "Role created and managed by OpenTofu")<br/><br/>    # Managed policy ARNs to attach<br/>    managed_policy_arns = optional(list(string))<br/><br/>    # Custom policies to create and attach<br/>    policies = optional(list(object({<br/>      name        = string<br/>      description = optional(string, "Policy created and managed by OpenTofu")<br/>      policy      = string<br/>    })), [])<br/><br/>    # Maximum session duration in seconds<br/>    max_session_duration = optional(number)<br/><br/>    # Path for the role<br/>    path = optional(string)<br/>  }))</pre> | `[]` | no |
| <a name="input_standalone_policies"></a> [standalone\_policies](#input\_standalone\_policies) | List of standalone IAM policies to create | <pre>list(object({<br/>    name        = string<br/>    description = optional(string)<br/>    policy      = string<br/>    path        = optional(string, "/")<br/>  }))</pre> | `[]` | no |
| <a name="input_tags"></a> [tags](#input\_tags) | Tags to be applied to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_policies"></a> [policies](#output\_policies) | Map of all created IAM policies |
| <a name="output_roles"></a> [roles](#output\_roles) | Map of created IAM roles |

## Module Summary

This module simplifies the creation and management of AWS IAM resources by providing a standardized way to define roles, attach policies, and maintain consistent naming conventions. It handles the complexity of IAM role and policy management with sensible defaults while allowing customization for specific use cases. The module is designed to follow AWS security best practices for IAM resource management.

## Features

- Creates IAM roles with customizable trust relationships (assume role policies)
- Supports attaching AWS managed policies to roles
- Creates and attaches custom IAM policies to roles
- Creates standalone IAM policies that can be referenced elsewhere
- Configurable session duration for IAM roles
- Consistent resource naming with customizable prefix
- Standardized tagging across all resources
- Flexible path configuration for organizing IAM resources
- Outputs role and policy ARNs, names, and IDs for easy reference

## Usage Examples

### Example 1: Basic IAM Setup

This example creates a simple Lambda execution role with AWS managed policies and a basic S3 bucket policy:

```hcl
module "basic_iam" {
  source = "sparrow-tofu-modules/components/aws/iam"

  name_prefix = "myapp-dev"

  # Create a Lambda execution role
  roles = [
    {
      name = "lambda-execution"
      description = "Role for Lambda functions to access AWS services"

      # Trust relationship allowing Lambda service to assume this role
      trust_relationship = jsonencode({
        Version = "2012-10-17"
        Statement = [
          {
            Effect = "Allow"
            Principal = {
              Service = "lambda.amazonaws.com"
            }
            Action = "sts:AssumeRole"
          }
        ]
      })

      # Attach AWS managed policies for common Lambda permissions
      managed_policy_arns = [
        "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
        "arn:aws:iam::aws:policy/AWSXRayDaemonWriteAccess"
      ]

      # Create a custom policy for S3 access
      policies = [
        {
          name = "s3-read-only"
          description = "Allows read-only access to application S3 bucket"
          policy = jsonencode({
            Version = "2012-10-17"
            Statement = [
              {
                Effect = "Allow"
                Action = [
                  "s3:GetObject",
                  "s3:ListBucket"
                ]
                Resource = [
                  "arn:aws:s3:::myapp-dev-bucket",
                  "arn:aws:s3:::myapp-dev-bucket/*"
                ]
              }
            ]
          })
        }
      ]
    }
  ]

  tags = {
    Environment = "development"
    ManagedBy = "terraform"
  }
}

# Use the outputs
output "lambda_role_arn" {
  value = module.basic_iam.roles["lambda-execution"].arn
}
```

### Example 2: Advanced IAM Configuration

This example demonstrates a more complex setup with multiple roles, custom session durations, and standalone policies:

```hcl
module "advanced_iam" {
  source = "sparrow-tofu-modules/components/aws/iam"

  name_prefix = "enterprise-platform"

  # Create multiple IAM roles with various configurations
  roles = [
    # ECS task execution role with enhanced permissions
    {
      name = "ecs-task-execution"
      description = "Enhanced ECS task execution role for container services"
      max_session_duration = 43200  # 12 hours
      path = "/service-roles/ecs/"

      trust_relationship = jsonencode({
        Version = "2012-10-17"
        Statement = [
          {
            Effect = "Allow"
            Principal = {
              Service = "ecs-tasks.amazonaws.com"
            }
            Action = "sts:AssumeRole"
            Condition = {
              ArnLike = {
                "aws:SourceArn": "arn:aws:ecs:*:************:*"
              }
            }
          }
        ]
      })

      managed_policy_arns = [
        "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
      ]

      policies = [
        {
          name = "secrets-access"
          description = "Access to specific secrets in Secrets Manager"
          policy = jsonencode({
            Version = "2012-10-17"
            Statement = [
              {
                Effect = "Allow"
                Action = [
                  "secretsmanager:GetSecretValue"
                ]
                Resource = [
                  "arn:aws:secretsmanager:*:************:secret:app/*"
                ]
              }
            ]
          })
        },
        {
          name = "kms-decrypt"
          policy = jsonencode({
            Version = "2012-10-17"
            Statement = [
              {
                Effect = "Allow"
                Action = [
                  "kms:Decrypt"
                ]
                Resource = "arn:aws:kms:*:************:key/*"
                Condition = {
                  StringEquals = {
                    "kms:ViaService": [
                      "secretsmanager.*.amazonaws.com"
                    ]
                  }
                }
              }
            ]
          })
        }
      ]
    },

    # Cross-account role for DevOps team
    {
      name = "devops-access"
      description = "Cross-account access for DevOps team"

      trust_relationship = jsonencode({
        Version = "2012-10-17"
        Statement = [
          {
            Effect = "Allow"
            Principal = {
              AWS = "arn:aws:iam::************:root"  # DevOps account
            }
            Action = "sts:AssumeRole"
            Condition = {
              Bool = {
                "aws:MultiFactorAuthPresent": "true"
              }
            }
          }
        ]
      })

      managed_policy_arns = [
        "arn:aws:iam::aws:policy/ReadOnlyAccess"
      ]

      policies = [
        {
          name = "emergency-access"
          description = "Break-glass permissions for emergencies"
          policy = jsonencode({
            Version = "2012-10-17"
            Statement = [
              {
                Effect = "Allow"
                Action = [
                  "ec2:RebootInstances",
                  "ecs:UpdateService",
                  "lambda:InvokeFunction"
                ]
                Resource = "*"
                Condition = {
                  StringEquals = {
                    "aws:PrincipalTag/Team": "SRE"
                  }
                }
              }
            ]
          })
        }
      ]
    }
  ]

  # Create standalone policies that can be attached elsewhere
  standalone_policies = [
    {
      name = "dynamodb-access"
      description = "Read/write access to specific DynamoDB tables"
      path = "/database-access/"
      policy = jsonencode({
        Version = "2012-10-17"
        Statement = [
          {
            Effect = "Allow"
            Action = [
              "dynamodb:GetItem",
              "dynamodb:PutItem",
              "dynamodb:UpdateItem",
              "dynamodb:DeleteItem",
              "dynamodb:Query",
              "dynamodb:Scan"
            ]
            Resource = [
              "arn:aws:dynamodb:*:************:table/users",
              "arn:aws:dynamodb:*:************:table/orders",
              "arn:aws:dynamodb:*:************:table/products"
            ]
          }
        ]
      })
    },
    {
      name = "cloudwatch-logs"
      description = "Allow writing to CloudWatch Logs"
      policy = jsonencode({
        Version = "2012-10-17"
        Statement = [
          {
            Effect = "Allow"
            Action = [
              "logs:CreateLogGroup",
              "logs:CreateLogStream",
              "logs:PutLogEvents",
              "logs:DescribeLogStreams"
            ]
            Resource = "arn:aws:logs:*:************:log-group:/aws/application/*"
          }
        ]
      })
    }
  ]

  tags = {
    Environment = "production"
    Department = "engineering"
    CostCenter = "platform"
    ManagedBy = "terraform"
  }
}

# Use the outputs to reference the created resources
output "ecs_role_arn" {
  value = module.advanced_iam.roles["ecs-task-execution"].arn
}

output "dynamodb_policy_arn" {
  value = module.advanced_iam.policies["dynamodb-access"].arn
}

# Attach standalone policy to an existing role
resource "aws_iam_role_policy_attachment" "attach_logs_policy" {
  role       = "existing-application-role"
  policy_arn = module.advanced_iam.policies["cloudwatch-logs"].arn
}
