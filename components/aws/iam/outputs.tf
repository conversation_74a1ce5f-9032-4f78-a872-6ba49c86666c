output "roles" {
  description = "Map of created IAM roles"
  value = {
    for role_name, role in aws_iam_role.this : role_name => {
      arn  = role.arn
      name = role.name
      id   = role.id
    }
  }
}

output "policies" {
  description = "Map of all created IAM policies"
  value = {
    for policy_key, policy in merge(aws_iam_policy.standalone, aws_iam_policy.custom) : policy_key => {
      arn  = policy.arn
      name = policy.name
      id   = policy.id
    }
  }
}
