variable "name_prefix" {
  type        = string
  description = "Prefix to be used for all resource names"
}

variable "tags" {
  type        = map(string)
  description = "Tags to be applied to all resources"
}

variable "roles" {
  type = list(object({
    name               = string
    trust_relationship = string
    description        = optional(string, "Role created and managed by OpenTofu")

    # Managed policy ARNs to attach
    managed_policy_arns = optional(list(string))

    # Custom policies to create and attach
    policies = optional(list(object({
      name        = string
      description = optional(string, "Policy created and managed by OpenTofu")
      policy      = string
    })), [])

    # Maximum session duration in seconds
    max_session_duration = optional(number)

    # Path for the role
    path = optional(string)
  }))

  description = "List of IAM roles to create with their policies"

  validation {
    condition     = length(var.roles) == length(distinct([for role in var.roles : role.name]))
    error_message = "Role names must be unique within the roles list."
  }
}

variable "standalone_policies" {
  type = list(object({
    name        = string
    description = optional(string)
    policy      = string
    path        = optional(string, "/")
  }))
  description = "List of standalone IAM policies to create"
  default     = []
}
