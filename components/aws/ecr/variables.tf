variable "name_prefix" {
  description = "A prefix to be added to all ECR repository names. Helps in organizing and identifying resources."
  type        = string
}

variable "ecr_repositories" {
  description = "A list of ECR repositories to create and configure."
  type = list(object({
    name                   = string
    image_tag_mutability   = optional(string) # Defaults to IMMUTABLE in locals
    kms_key_arn            = optional(string) # ARN of the KMS key for KMS encryption. If not provided, alias/aws/ecr is used.
    lifecycle_policy_json  = optional(string) # JSON string for the ECR lifecycle policy. A default policy is applied if not provided.
    repository_policy_json = optional(string) # JSON string for the ECR repository policy.
    force_delete           = optional(bool)   # Specifies whether force delete is enabled on the repository or not. Defaults to false.
    tags                   = optional(map(string), {})
  }))
  default = []
}

variable "tags" {
  description = "A map of common tags to add to all created ECR repositories."
  type        = map(string)
}
