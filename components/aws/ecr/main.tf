data "aws_kms_alias" "ecr_default" {
  name = "alias/aws/ecr"
}

locals {
  # Default ECR settings (Secure by default)
  default_ecr_settings = {
    image_tag_mutability   = "IMMUTABLE"
    force_delete           = false
    lifecycle_policy_json  = file("${path.module}/files/default_lifecycle_policy.json")
    repository_policy_json = null
    kms_key_arn            = null # Placeholder, actual default logic in processed_repositories
    tags                   = {}
  }

  processed_repositories = {
    for repo_config in var.ecr_repositories : repo_config.name => merge(
      local.default_ecr_settings,
      { # Handle nulls from optional attributes before merging user-provided values
        image_tag_mutability   = repo_config.image_tag_mutability == null ? local.default_ecr_settings.image_tag_mutability : repo_config.image_tag_mutability,
        force_delete           = repo_config.force_delete == null ? local.default_ecr_settings.force_delete : repo_config.force_delete,
        lifecycle_policy_json  = repo_config.lifecycle_policy_json == null ? local.default_ecr_settings.lifecycle_policy_json : repo_config.lifecycle_policy_json,
        repository_policy_json = repo_config.repository_policy_json,
        # kms_key_arn is handled in derived values section for defaulting
        tags = repo_config.tags == null ? local.default_ecr_settings.tags : repo_config.tags
      },
      {                                 # Derived values
        derived_encryption_type = "KMS" # Always KMS
        derived_kms_key         = (repo_config.kms_key_arn != null && repo_config.kms_key_arn != "") ? repo_config.kms_key_arn : data.aws_kms_alias.ecr_default.target_key_arn
        final_repo_name         = "${var.name_prefix}-${repo_config.name}-ecr"
      }
    )
  }
}

resource "aws_ecr_repository" "this" {
  for_each = local.processed_repositories

  name                 = each.value.final_repo_name
  image_tag_mutability = each.value.image_tag_mutability
  force_delete         = each.value.force_delete

  image_scanning_configuration {
    scan_on_push = true # Always true
  }

  encryption_configuration {
    encryption_type = each.value.derived_encryption_type # Should always be KMS now
    kms_key         = each.value.derived_kms_key
  }

  tags = merge(var.tags, each.value.tags, { Name = each.value.final_repo_name })
}

resource "aws_ecr_lifecycle_policy" "this" {
  for_each = { for k, v in local.processed_repositories : k => v if v.lifecycle_policy_json != null }

  repository = aws_ecr_repository.this[each.key].name
  policy     = each.value.lifecycle_policy_json
}

resource "aws_ecr_repository_policy" "this" {
  for_each = { for k, v in local.processed_repositories : k => v if v.repository_policy_json != null }

  repository = aws_ecr_repository.this[each.key].name
  policy     = each.value.repository_policy_json
}
