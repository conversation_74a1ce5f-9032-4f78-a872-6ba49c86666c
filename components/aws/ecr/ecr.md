# AWS ECR Repository Terraform Module

This module creates and manages AWS Elastic Container Registry (ECR) repositories with a focus on security and ease of use for application developers.
It uses a `name_prefix` to ensure consistent naming conventions.

## Features

-   **Consistent Naming**: Uses a `name_prefix` (required) for all repository names.
-   **Secure by Default**: Repositories are created with security best practices:
    -   **KMS Encryption Always Enabled**: Encryption is always performed using AWS KMS. If a specific `kms_key_arn` is provided for a repository, that key is used. Otherwise, the default AWS-managed KMS key for ECR (`alias/aws/ecr`) is used.
    -   **Image Tag Immutability**: Enabled by default (`IMMUTABLE`). Can be overridden to `MUTABLE`.
    -   **Scan on Push Always Enabled**: Images are always scanned for vulnerabilities upon being pushed to the repository. This is not configurable.
-   **Simplified Configuration**: Complex ECR configurations are abstracted, requiring minimal input.
-   **List-based Input**: Define multiple repositories through a single list variable.
-   **Customizable**: While key security features are enforced or strongly defaulted, aspects like lifecycle policies, repository policies, and specific KMS keys can be customized.
-   **Default Lifecycle Policy**: Includes a default lifecycle policy, read from `default_lifecycle_policy.json`, to expire untagged images older than 14 days. This can be overridden.

## Usage Example

```terraform
module "ecr_repositories" {
  source = "./components/aws/ecr"  # Or your module source path

  name_prefix = "my-org-app" # e.g., my-org-app-frontend

  tags = { # Common tags, required
    Environment = "production"
    Project     = "my-app-project"
  }

  ecr_repositories = [
    {
      name        = "frontend" # Actual name will be my-org-app-frontend
      # Uses default AWS-managed KMS key for ECR (alias/aws/ecr).
      # Scan on push is always enabled.
      # Uses default lifecycle policy (from default_lifecycle_policy.json).
      tags = {
        Team = "frontend-devs"
      }
    },
    {
      name                  = "backend" # Actual name will be my-org-app-backend
      image_tag_mutability  = "MUTABLE" # Example: Override default immutability
      kms_key_arn           = "arn:aws:kms:us-east-1:************:key/your-custom-kms-key-id" # Use a specific KMS key
      lifecycle_policy_json = file("${path.module}/custom-lifecycle-policy.json") # Override default policy
      repository_policy_json= jsonencode({
        Version = "2012-10-17",
        Statement = [
          {
            Sid    = "AllowCrossAccountPull",
            Effect = "Allow",
            Principal = {
              AWS = "arn:aws:iam::************:root"
            },
            Action = [
              "ecr:GetDownloadUrlForLayer",
              "ecr:BatchGetImage",
              "ecr:BatchCheckLayerAvailability"
            ]
          }
        ]
      })
      force_delete          = false
      tags = {
        Team = "backend-devs"
      }
    }
  ]
}
```

## Inputs

| Name               | Description                                                                                                | Type                | Default                                                                    | Required |
| ------------------ | ---------------------------------------------------------------------------------------------------------- | ------------------- | -------------------------------------------------------------------------- | :------: |
| `name_prefix`      | A prefix to be added to all ECR repository names. Helps in organizing and identifying resources.           | `string`            |                                                                            |   Yes    |
| `ecr_repositories` | A list of ECR repositories to create and configure. See attributes below.                                  | `list(object)`      | `[]`                                                                       |    No    |
| `tags`             | A map of common tags to add to all created ECR repositories.                                               | `map(string)`       |                                                                            |   Yes    |

### `ecr_repositories` Object Attributes

| Name                     | Description                                                                                                   | Type          | Default (from locals or behavior)                                           | Required |
| ------------------------ | ------------------------------------------------------------------------------------------------------------- | ------------- | --------------------------------------------------------------------------- | :------: |
| `name`                   | The base name of the ECR repository. The `name_prefix` will be prepended to this value.                      | `string`      |                                                                             |   Yes    |
| `image_tag_mutability`   | The tag mutability setting for the repository. Can be `MUTABLE` or `IMMUTABLE`.                               | `string`      | `"IMMUTABLE"`                                                               |    No    |
| `kms_key_arn`            | The ARN of the AWS KMS key to use for encryption. If not provided, the default AWS-managed KMS key for ECR (`alias/aws/ecr`) is used. | `string`      | `alias/aws/ecr` (via data source)                                           |    No    |
| `lifecycle_policy_json`  | A JSON string defining the ECR lifecycle policy. If not provided, the policy from `default_lifecycle_policy.json` is applied. | `string`      | Content of `default_lifecycle_policy.json`                                  |    No    |
| `repository_policy_json` | A JSON string defining the ECR repository policy.                                                             | `string`      | `null` (no policy)                                                          |    No    |
| `force_delete`           | If `true`, will delete the repository even if it contains images. Use with caution.                             | `bool`        | `false`                                                                     |    No    |
| `tags`                   | A map of tags specific to this repository, merged with the common `var.tags`.                                 | `map(string)` | `{}`                                                                        |    No    |

**Note on `scan_on_push`**: Image scanning on push is always enabled by this module and is not a configurable option.

**Default Lifecycle Policy (`default_lifecycle_policy.json`):**

The module uses a default lifecycle policy defined in `components/aws/ecr/default_lifecycle_policy.json`. This policy is applied if `lifecycle_policy_json` is not specified for a repository. By default, it expires untagged images older than 14 days.

Content of `default_lifecycle_policy.json`:
```json
{
  "rules": [
    {
      "rulePriority": 1,
      "description": "Expire untagged images older than 14 days",
      "selection": {
        "tagStatus": "untagged",
        "countType": "sinceImagePushed",
        "countUnit": "days",
        "countNumber": 14
      },
      "action": {
        "type": "expire"
      }
    }
  ]
}
```

## Outputs

| Name                                | Description                                                                                                   | Type                                          | Sensitive |
| ----------------------------------- | ------------------------------------------------------------------------------------------------------------- | --------------------------------------------- | :-------: |
| `ecr_repositories_details`          | A map of created ECR repositories, keyed by their *original* name (from the input list, not the prefixed name). Each value contains `arn`, `name` (prefixed), `repository_url`, and `registry_id`. | `map(object)`                                 |   False   |
| `ecr_repository_lifecycle_policies` | A map of applied ECR lifecycle policies, keyed by their *original* name. Each value contains `repository_name` (prefixed), `registry_id`, and `policy_json`. | `map(object)`                                 |   False   |
| `ecr_repository_policies`           | A map of applied ECR repository policies, keyed by their *original* name. Each value contains `repository_name` (prefixed), `registry_id`, and `policy_json`. | `map(object)`                                 |   True    |
