output "ecr_repositories_details" {
  description = "Details of the created ECR repositories."
  value = {
    for k, repo in aws_ecr_repository.this : k => {
      arn            = repo.arn
      name           = repo.name
      repository_url = repo.repository_url
      registry_id    = repo.registry_id
    }
  }
}

output "ecr_repository_lifecycle_policies" {
  description = "Details of the ECR lifecycle policies applied."
  value = {
    for k, policy in aws_ecr_lifecycle_policy.this : k => {
      repository_name = policy.repository
      registry_id     = policy.registry_id
      policy_json     = policy.policy # Contains the JSON text of the policy
    }
  }
  sensitive = false # Lifecycle policies are not typically sensitive
}

output "ecr_repository_policies" {
  description = "Details of the ECR repository policies applied."
  value = {
    for k, policy in aws_ecr_repository_policy.this : k => {
      repository_name = policy.repository
      registry_id     = policy.registry_id
      policy_json     = policy.policy # Contains the JSON text of the policy
    }
  }
  sensitive = true # Repository policies can contain sensitive information like account IDs
}
