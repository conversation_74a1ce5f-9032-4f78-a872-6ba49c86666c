variable "name_prefix" {
  description = "Prefix to be used in resource names"
  type        = string
}

variable "secrets" {
  description = "List of secrets to create"
  type = list(object({
    name                    = string
    secret_string           = string
    description             = optional(string)
    kms_key_arn             = optional(string)
    recovery_window_in_days = optional(number)
    rotation_lambda_arn     = optional(string)
    rotation_days           = optional(number)
  }))

  validation {
    condition     = length(var.secrets) == length(distinct([for secret in var.secrets : secret.name]))
    error_message = "Secret names must be unique within the list."
  }
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
}
