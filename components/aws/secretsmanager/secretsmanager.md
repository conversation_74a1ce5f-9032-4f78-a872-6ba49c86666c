## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.9 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.0 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_secretsmanager_secret.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/secretsmanager_secret) | resource |
| [aws_secretsmanager_secret_rotation.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/secretsmanager_secret_rotation) | resource |
| [aws_secretsmanager_secret_version.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/secretsmanager_secret_version) | resource |
| [aws_kms_key.secretsmanager](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/kms_key) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_name_prefix"></a> [name\_prefix](#input\_name\_prefix) | Prefix to be used in resource names | `string` | n/a | yes |
| <a name="input_secrets"></a> [secrets](#input\_secrets) | List of secrets to create | <pre>list(object({<br/>    name                        = string<br/>    description                 = optional(string)<br/>    kms_key_arn                 = optional(string)<br/>    recovery_window_in_days     = optional(number)<br/>    secret_string               = optional(string)<br/>    rotation_lambda_arn         = optional(string)<br/>    rotation_days               = optional(number)<br/>  }))</pre> | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | A map of tags to add to all resources | `map(string)` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_secret_arns"></a> [secret\_arns](#output\_secret\_arns) | Map of secret names to their ARNs |
| <a name="output_secret_ids"></a> [secret\_ids](#output\_secret\_ids) | Map of secret names to their IDs |
| <a name="output_secret_names"></a> [secret\_names](#output\_secret\_names) | Map of secret names to their full names including prefix |
| <a name="output_secret_version_ids"></a> [secret\_version\_ids](#output\_secret\_version\_ids) | Map of secret names to their version IDs (only for secrets with versions) |

## Module Summary

This AWS Secrets Manager module provides a standardized approach to creating and managing secrets in AWS Secrets Manager. It simplifies the process of setting up encrypted secrets with consistent naming, appropriate encryption, and optional automatic rotation. The module handles the complexity of AWS Secrets Manager configuration while providing sensible defaults and flexibility for customization.

## Features

- Creates multiple secrets with a single module call
- Enforces consistent naming convention with customizable prefix
- Uses AWS KMS for encryption with default AWS-managed key or custom key
- Supports optional secret rotation configuration with Lambda functions
- Configurable recovery window for deleted secrets
- Creates secret versions with provided secret values
- Validates uniqueness of secret names
- Consistent tagging across all resources
- Lifecycle management to prevent disruption during updates
- Provides comprehensive outputs for secret ARNs, IDs, and version information

## Usage Examples

### Example 1:
This example shows how to create simple secrets for database credentials:

```hcl
module "basic_secrets" {
  source = "sparrow-tofu-modules/components/aws/secretsmanager"

  name_prefix = "myapp-dev"

  secrets = [
    {
      name = "database-credentials"
      description = "PostgreSQL database credentials for application"
      secret_string = jsonencode({
        username = "dbadmin"
        password = "ExamplePassword123!"  # In production, use a secure method to handle this value
        host     = "mydb.example.com"
        port     = 5432
        dbname   = "appdb"
      })
      # Using default KMS key and no rotation
    },
    {
      name = "api-key"
      description = "External API authentication key"
      secret_string = "example-api-key-value"  # In production, use a secure method
      recovery_window_in_days = 7  # Custom recovery window
    }
  ]

  tags = {
    Environment = "development"
    Application = "MyApp"
    ManagedBy   = "Terraform"
  }
}

# Access the secrets in other resources
output "db_secret_arn" {
  value = module.basic_secrets.secret_arns["database-credentials"]
}

# Example of referencing the secret in an application
resource "aws_ecs_task_definition" "app" {
  # ... other configuration ...

  container_definitions = jsonencode([
    {
      name = "app-container"
      # ... other settings ...

      secrets = [
        {
          name      = "DB_CONNECTION_STRING"
          valueFrom = "${module.basic_secrets.secret_arns["database-credentials"]}"
        }
      ]
    }
  ])
}
```

### Example 2:

This example demonstrates advanced features including custom KMS keys, secret rotation, and multiple secret types:

```hcl
module "advanced_secrets" {
  source = "sparrow-tofu-modules/components/aws/secretsmanager"

  name_prefix = "production-platform"

  secrets = [
    # Database credentials with automatic rotation
    {
      name = "rds-master-credentials"
      description = "RDS PostgreSQL master credentials with rotation"
      kms_key_arn = "arn:aws:kms:us-west-2:123456789012:key/abcd1234-ab12-cd34-ef56-abcdef123456"  # Custom KMS key
      secret_string = jsonencode({
        engine   = "postgres"
        username = "admin"
        password = "SecureP@ssword!"  # In production, use a secure method
        host     = "db.internal.example.com"
        port     = 5432
        dbname   = "primarydb"
      })
      rotation_lambda_arn = "arn:aws:lambda:us-west-2:123456789012:function:rotate-rds-credentials"
      rotation_days = 30  # Rotate every 30 days
      recovery_window_in_days = 14  # Longer recovery period for production
    },

    # Application-specific secrets
    {
      name = "payment-gateway-keys"
      description = "Payment processor API credentials"
      kms_key_arn = "arn:aws:kms:us-west-2:123456789012:key/payment-key-1234"
      secret_string = jsonencode({
        api_key      = "live_key_123456789"
        api_secret   = "live_secret_abcdefghijk"
        webhook_key  = "whsec_1234567890abcdefghijk"
        environment  = "production"
        merchant_id  = "merch_12345"
      })
      # No automatic rotation - managed manually
      recovery_window_in_days = 30  # Longer recovery period for critical secrets
    },

    # OAuth client credentials
    {
      name = "oauth-client-secrets"
      description = "OAuth 2.0 client credentials for service-to-service authentication"
      secret_string = jsonencode({
        client_id     = "client_12345"
        client_secret = "secret_abcdefghijklmnopqrstuvwxyz"
        tenant_id     = "tenant_67890"
        authority     = "https://login.example.com/tenant_67890"
        scopes        = ["api.read", "api.write"]
      })
      # Using default KMS key
      rotation_lambda_arn = "arn:aws:lambda:us-west-2:123456789012:function:rotate-oauth-credentials"
      rotation_days = 90  # Rotate quarterly
    },

    # Empty secret (placeholder to be filled by another process)
    {
      name = "runtime-credentials"
      description = "Runtime-generated credentials - populated by application bootstrapping process"
      # No secret_string provided - will be populated later
    }
  ]

  tags = {
    Environment = "production"
    CostCenter  = "platform-security"
    Owner       = "security-team"
    ManagedBy   = "Terraform"
    Compliance  = "PCI-DSS"
  }
}

```
