data "aws_kms_key" "secretsmanager" {
  key_id = "alias/aws/secretsmanager"
}

locals {
  # Convert secrets list to map for easier lookup
  secrets_map = {
    for idx, secret in var.secrets : secret.name => secret
  }

  default_secret_settings = {
    recovery_window_in_days = 0
    enable_rotation         = false
    rotation_days           = 90
  }

  default_kms_arn = data.aws_kms_key.secretsmanager.arn
}

resource "aws_secretsmanager_secret" "this" {
  for_each = local.secrets_map

  name                    = "${var.name_prefix}-${each.value.name}"
  description             = try(each.value.description, "Secret created and managed by OpenTofu")
  kms_key_id              = try(each.value.kms_key_arn, local.default_kms_arn)
  recovery_window_in_days = try(each.value.recovery_window_in_days, local.default_secret_settings.recovery_window_in_days)

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-${each.key}-secret"
  })
}

resource "aws_secretsmanager_secret_version" "this" {
  for_each = local.secrets_map

  secret_id     = aws_secretsmanager_secret.this[each.key].id
  secret_string = each.value.secret_string

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_secretsmanager_secret_rotation" "this" {
  for_each = {
    for name, secret in local.secrets_map : name => secret
    if coalesce(secret.rotation_lambda_arn, local.default_secret_settings.enable_rotation)
  }

  secret_id           = aws_secretsmanager_secret.this[each.key].id
  rotation_lambda_arn = each.value.rotation_lambda_arn

  rotation_rules {
    automatically_after_days = try(each.value.rotation_days, local.default_secret_settings.rotation_days)
  }
}
