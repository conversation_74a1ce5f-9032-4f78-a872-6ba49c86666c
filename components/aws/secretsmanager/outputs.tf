output "secret_arns" {
  description = "Map of secret names to their ARNs"
  value       = { for k, v in aws_secretsmanager_secret.this : k => v.arn }
}

output "secret_ids" {
  description = "Map of secret names to their IDs"
  value       = { for k, v in aws_secretsmanager_secret.this : k => v.id }
}

output "secret_names" {
  description = "Map of secret names to their full names including prefix"
  value       = { for k, v in aws_secretsmanager_secret.this : k => v.name }
}

output "secret_version_ids" {
  description = "Map of secret names to their version IDs (only for secrets with versions)"
  value       = { for k, v in aws_secretsmanager_secret_version.this : k => v.version_id }
}
