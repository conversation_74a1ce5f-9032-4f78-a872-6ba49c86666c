# sparrow-tofu-modules/components/aws/kms/variables.tf

variable "name_prefix" {
  description = "A prefix for all resource names to ensure uniqueness and grouping. If not provided, only the key name will be used."
  type        = string
  default     = ""
  nullable    = false
}

variable "tags" {
  description = "A map of tags to assign to all created resources."
  type        = map(string)
  default     = {}
  nullable    = false
}

variable "keys" {
  description = "List of KMS keys to create."
  type = list(object({
    name        = string
    description = optional(string)

    # Key configuration
    key_usage = optional(string, "ENCRYPT_DECRYPT")   # ENCRYPT_DECRYPT, SIGN_VERIFY, GENERATE_VERIFY_MAC
    key_spec  = optional(string, "SYMMETRIC_DEFAULT") # SYMMETRIC_DEFAULT, RSA_2048, RSA_3072, RSA_4096, ECC_NIST_P256, ECC_NIST_P384, ECC_NIST_P521, ECC_SECG_P256K1, HMAC_224, HMAC_256, HMAC_384, HMAC_512, SM2
    enabled   = optional(bool, true)

    # Deletion configuration
    deletion_window_in_days = optional(number, 30)

    # Rotation configuration
    enable_key_rotation = optional(bool, true)

    # Multi-region configuration
    multi_region = optional(bool, false)

    # Alias configuration
    create_alias = optional(bool, true)

    # Key policy (JSON string)
    policy = optional(string)

    # Cross-account access configuration
    cross_account_access = optional(list(object({
      principals = list(string)
      actions    = list(string)
      conditions = optional(list(object({
        test     = string
        variable = string
        values   = list(string)
      })), [])
    })), [])

    # Service access configuration
    service_access = optional(list(object({
      services = list(string)
      actions  = list(string)
      conditions = optional(list(object({
        test     = string
        variable = string
        values   = list(string)
      })), [])
    })), [])

    # KMS grants configuration
    grants = optional(list(object({
      grantee_principal  = string
      operations         = list(string)
      name               = optional(string)
      retiring_principal = optional(string)
      constraints = optional(object({
        encryption_context_equals = optional(map(string))
        encryption_context_subset = optional(map(string))
      }))
    })), [])

    # External key material (for imported keys)
    key_material_base64 = optional(string)
    valid_to            = optional(string)

    # Replica regions for multi-region keys
    replica_regions = optional(list(object({
      region                  = string
      deletion_window_in_days = optional(number, 30)
      enabled                 = optional(bool, true)
      policy                  = optional(string)
      tags                    = optional(map(string), {})
    })), [])

    # Additional tags specific to this key
    tags = optional(map(string), {})
  }))
  default = []

  validation {
    condition = alltrue([
      for key in var.keys : length(key.name) > 0 && length(key.name) <= 256
    ])
    error_message = "Key name must be between 1 and 256 characters."
  }

  validation {
    condition = alltrue([
      for key in var.keys : can(regex("^[a-zA-Z0-9/_.-]+$", key.name))
    ])
    error_message = "Key name can only contain alphanumeric characters, forward slashes (/), underscores (_), periods (.), and hyphens (-)."
  }

  validation {
    condition = alltrue([
      for key in var.keys : contains([
        "ENCRYPT_DECRYPT",
        "SIGN_VERIFY",
        "GENERATE_VERIFY_MAC"
      ], lookup(key, "key_usage", "ENCRYPT_DECRYPT"))
    ])
    error_message = "Key usage must be one of: ENCRYPT_DECRYPT, SIGN_VERIFY, GENERATE_VERIFY_MAC."
  }

  validation {
    condition = alltrue([
      for key in var.keys : contains([
        "SYMMETRIC_DEFAULT",
        "RSA_2048", "RSA_3072", "RSA_4096",
        "ECC_NIST_P256", "ECC_NIST_P384", "ECC_NIST_P521", "ECC_SECG_P256K1",
        "HMAC_224", "HMAC_256", "HMAC_384", "HMAC_512",
        "SM2"
      ], lookup(key, "key_spec", "SYMMETRIC_DEFAULT"))
    ])
    error_message = "Key spec must be a valid AWS KMS key spec."
  }

  validation {
    condition = alltrue([
      for key in var.keys : (
        lookup(key, "deletion_window_in_days", 30) >= 7 &&
        lookup(key, "deletion_window_in_days", 30) <= 30
      )
    ])
    error_message = "Deletion window must be between 7 and 30 days."
  }

  validation {
    condition = alltrue([
      for key in var.keys : (
        lookup(key, "key_usage", "ENCRYPT_DECRYPT") == "ENCRYPT_DECRYPT" ||
        lookup(key, "enable_key_rotation", true) == false
      )
    ])
    error_message = "Key rotation can only be enabled for ENCRYPT_DECRYPT keys."
  }

  validation {
    condition = alltrue([
      for key in var.keys : (
        lookup(key, "key_spec", "SYMMETRIC_DEFAULT") == "SYMMETRIC_DEFAULT" ||
        lookup(key, "multi_region", false) == false
      )
    ])
    error_message = "Multi-region keys must use SYMMETRIC_DEFAULT key spec."
  }

  validation {
    condition = alltrue([
      for key in var.keys : (
        lookup(key, "key_material_base64", null) == null ||
        lookup(key, "enable_key_rotation", true) == false
      )
    ])
    error_message = "Key rotation cannot be enabled for keys with imported key material."
  }

  validation {
    condition = alltrue([
      for key in var.keys :
      alltrue([
        for grant in lookup(key, "grants", []) :
        alltrue([
          for operation in grant.operations :
          contains([
            "Decrypt", "DescribeKey", "Encrypt", "GenerateDataKey", "GenerateDataKeyWithoutPlaintext",
            "ReEncryptFrom", "ReEncryptTo", "CreateGrant", "RetireGrant", "DescribeKey", "GenerateDataKeyPair",
            "GenerateDataKeyPairWithoutPlaintext", "GetPublicKey", "Sign", "Verify", "GenerateMac", "VerifyMac"
          ], operation)
        ])
      ])
    ])
    error_message = "Grant operations must be valid KMS operations."
  }

  validation {
    condition = alltrue([
      for key in var.keys : (
        lookup(key, "multi_region", false) == false ||
        length(lookup(key, "replica_regions", [])) > 0
      )
    ])
    error_message = "Multi-region keys must specify at least one replica region."
  }

  validation {
    condition = alltrue([
      for key in var.keys :
      alltrue([
        for replica in lookup(key, "replica_regions", []) : (
          replica.deletion_window_in_days >= 7 &&
          replica.deletion_window_in_days <= 30
        )
      ])
    ])
    error_message = "Replica key deletion window must be between 7 and 30 days."
  }
}
