# ------------------------------------------------------------------------------
# KMS Module Outputs
# ------------------------------------------------------------------------------

# ------------------------------------------------------------------------------
# Primary Key Outputs
# ------------------------------------------------------------------------------

output "key_details" {
  description = "Details of all created KMS keys."
  value = {
    for k, key in aws_kms_key.this : k => {
      key_id               = key.key_id
      arn                  = key.arn
      description          = key.description
      key_usage            = key.key_usage
      key_spec             = key.customer_master_key_spec
      enabled              = key.is_enabled
      key_rotation_enabled = key.enable_key_rotation
      multi_region         = key.multi_region
      deletion_window      = key.deletion_window_in_days
      tags                 = key.tags
    }
  }
}

output "key_ids" {
  description = "Map of key names to their IDs."
  value       = { for k, key in aws_kms_key.this : k => key.key_id }
}

output "key_arns" {
  description = "Map of key names to their ARNs."
  value       = { for k, key in aws_kms_key.this : k => key.arn }
}

output "alias_names" {
  description = "Map of key names to their alias names."
  value       = { for k, alias in aws_kms_alias.this : k => alias.name }
}

output "alias_arns" {
  description = "Map of key names to their alias ARNs."
  value       = { for k, alias in aws_kms_alias.this : k => alias.arn }
}

output "external_key_details" {
  description = "Details of all created external KMS keys."
  value = {
    for k, key in aws_kms_external_key.this : k => {
      key_id           = key.key_id
      arn              = key.arn
      description      = key.description
      enabled          = key.enabled
      expiration_model = key.expiration_model
      key_state        = key.key_state
      key_usage        = key.key_usage
      valid_to         = key.valid_to
      deletion_window  = key.deletion_window_in_days
      tags             = key.tags
    }
  }
}

output "external_key_ids" {
  description = "Map of external key names to their IDs."
  value       = { for k, key in aws_kms_external_key.this : k => key.key_id }
}

output "external_key_arns" {
  description = "Map of external key names to their ARNs."
  value       = { for k, key in aws_kms_external_key.this : k => key.arn }
}

output "replica_key_details" {
  description = "Details of all created replica keys."
  value = {
    for k, key in aws_kms_replica_key.this : k => {
      key_id               = key.key_id
      arn                  = key.arn
      description          = key.description
      enabled              = key.enabled
      key_rotation_enabled = key.key_rotation_enabled
      key_spec             = key.key_spec
      key_usage            = key.key_usage
      primary_key_arn      = key.primary_key_arn
      deletion_window      = key.deletion_window_in_days
      tags                 = key.tags
    }
  }
}

output "replica_key_ids" {
  description = "Map of replica key names to their IDs."
  value       = { for k, key in aws_kms_replica_key.this : k => key.key_id }
}

output "replica_key_arns" {
  description = "Map of replica key names to their ARNs."
  value       = { for k, key in aws_kms_replica_key.this : k => key.arn }
}

output "grant_details" {
  description = "Details of all created KMS grants."
  value = {
    for k, grant in aws_kms_grant.this : k => {
      grant_id           = grant.grant_id
      key_id             = grant.key_id
      grantee_principal  = grant.grantee_principal
      operations         = grant.operations
      name               = grant.name
      retiring_principal = grant.retiring_principal
      creation_date      = grant.creation_date
      grant_token        = grant.grant_token
    }
  }
}

output "grant_ids" {
  description = "Map of grant names to their IDs."
  value       = { for k, grant in aws_kms_grant.this : k => grant.grant_id }
}

output "grant_tokens" {
  description = "Map of grant names to their tokens."
  value       = { for k, grant in aws_kms_grant.this : k => grant.grant_token }
  sensitive   = true
}

# Convenience outputs for common use cases
output "encryption_keys" {
  description = "Map of encryption key names to their ARNs (for use in other modules)."
  value = {
    for k, key in aws_kms_key.this : k => key.arn
    if lookup(var.keys[k], "key_usage", "ENCRYPT_DECRYPT") == "ENCRYPT_DECRYPT"
  }
}

output "signing_keys" {
  description = "Map of signing key names to their ARNs."
  value = {
    for k, key in aws_kms_key.this : k => key.arn
    if lookup(var.keys[k], "key_usage", "ENCRYPT_DECRYPT") == "SIGN_VERIFY"
  }
}

output "mac_keys" {
  description = "Map of MAC key names to their ARNs."
  value = {
    for k, key in aws_kms_key.this : k => key.arn
    if lookup(var.keys[k], "key_usage", "ENCRYPT_DECRYPT") == "GENERATE_VERIFY_MAC"
  }
}

# Application configuration output
output "key_configuration" {
  description = "Configuration values for applications using these keys."
  value = {
    key_ids     = { for k, key in aws_kms_key.this : k => key.key_id }
    key_arns    = { for k, key in aws_kms_key.this : k => key.arn }
    alias_names = { for k, alias in aws_kms_alias.this : k => alias.name }
    alias_arns  = { for k, alias in aws_kms_alias.this : k => alias.arn }

    # Grouped by usage type
    encryption_keys = {
      for k, key in aws_kms_key.this : k => {
        key_id    = key.key_id
        arn       = key.arn
        alias_arn = try(aws_kms_alias.this[k].arn, null)
      }
      if lookup(var.keys[k], "key_usage", "ENCRYPT_DECRYPT") == "ENCRYPT_DECRYPT"
    }

    signing_keys = {
      for k, key in aws_kms_key.this : k => {
        key_id    = key.key_id
        arn       = key.arn
        alias_arn = try(aws_kms_alias.this[k].arn, null)
      }
      if lookup(var.keys[k], "key_usage", "ENCRYPT_DECRYPT") == "SIGN_VERIFY"
    }

    mac_keys = {
      for k, key in aws_kms_key.this : k => {
        key_id    = key.key_id
        arn       = key.arn
        alias_arn = try(aws_kms_alias.this[k].arn, null)
      }
      if lookup(var.keys[k], "key_usage", "ENCRYPT_DECRYPT") == "GENERATE_VERIFY_MAC"
    }
  }
}

# Summary output
output "summary" {
  description = "Summary of created KMS resources."
  value = {
    total_keys          = length(aws_kms_key.this)
    total_external_keys = length(aws_kms_external_key.this)
    total_replica_keys  = length(aws_kms_replica_key.this)
    total_aliases       = length(aws_kms_alias.this) + length(aws_kms_alias.external)
    total_grants        = length(aws_kms_grant.this)

    keys_by_usage = {
      encrypt_decrypt     = length([for k, key in var.keys : k if lookup(key, "key_usage", "ENCRYPT_DECRYPT") == "ENCRYPT_DECRYPT"])
      sign_verify         = length([for k, key in var.keys : k if lookup(key, "key_usage", "ENCRYPT_DECRYPT") == "SIGN_VERIFY"])
      generate_verify_mac = length([for k, key in var.keys : k if lookup(key, "key_usage", "ENCRYPT_DECRYPT") == "GENERATE_VERIFY_MAC"])
    }

    multi_region_keys  = length([for k, key in var.keys : k if lookup(key, "multi_region", false)])
    keys_with_rotation = length([for k, key in var.keys : k if lookup(key, "enable_key_rotation", true)])
  }
}
