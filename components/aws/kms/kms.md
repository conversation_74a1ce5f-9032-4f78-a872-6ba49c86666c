# AWS KMS Module Technical Reference

This document provides detailed technical information about the AWS KMS Terraform module.

## Table of Contents

- [Overview](#overview)
- [Resources Created](#resources-created)
- [Input Variables](#input-variables)
- [Outputs](#outputs)
- [Key Configuration](#key-configuration)
- [Examples](#examples)
- [Security Considerations](#security-considerations)

## Overview

This module creates AWS KMS (Key Management Service) keys with comprehensive configuration options including:

- Multiple key types (encryption, signing, MAC)
- Multi-region key support
- Cross-account access controls
- Service-specific permissions
- KMS grants for fine-grained access
- External key material support (BYOK)
- Automatic key rotation
- Comprehensive tagging

## Resources Created

| Resource Type | Description |
|---------------|-------------|
| `aws_kms_key` | Primary KMS keys |
| `aws_kms_alias` | Key aliases for easy reference |
| `aws_kms_grant` | Fine-grained access permissions |
| `aws_kms_external_key` | Keys with imported material |
| `aws_kms_replica_key` | Multi-region key replicas |
| `aws_iam_policy_document` | Default key policies |

## Input Variables

### Required Variables

None. All variables have sensible defaults.

### Optional Variables

#### Core Configuration

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| name_prefix | A prefix for all resource names | `string` | `""` | no |
| tags | A map of tags to assign to all resources | `map(string)` | `{}` | no |
| keys | List of KMS keys to create | `list(object)` | `[]` | no |

#### Key Configuration Object

Each key in the `keys` list supports the following attributes:

| Attribute | Description | Type | Default | Required |
|-----------|-------------|------|---------|:--------:|
| name | Name of the key | `string` | n/a | yes |
| description | Description of the key | `string` | Auto-generated | no |
| key_usage | Key usage type | `string` | `"ENCRYPT_DECRYPT"` | no |
| key_spec | Key specification | `string` | `"SYMMETRIC_DEFAULT"` | no |
| enabled | Whether the key is enabled | `bool` | `true` | no |
| deletion_window_in_days | Deletion window in days | `number` | `30` | no |
| enable_key_rotation | Enable automatic key rotation | `bool` | `true` | no |
| multi_region | Create as multi-region key | `bool` | `false` | no |
| create_alias | Create alias for the key | `bool` | `true` | no |
| policy | Custom key policy as JSON string | `string` | Auto-generated | no |
| cross_account_access | Cross-account access configuration | `list(object)` | `[]` | no |
| service_access | AWS service access configuration | `list(object)` | `[]` | no |
| grants | KMS grants configuration | `list(object)` | `[]` | no |
| key_material_base64 | External key material (base64) | `string` | `null` | no |
| valid_to | Expiration date for external key material | `string` | `null` | no |
| replica_regions | Replica regions for multi-region keys | `list(object)` | `[]` | no |
| tags | Additional tags specific to this key | `map(string)` | `{}` | no |

### Key Usage Types

| Value | Description | Use Cases |
|-------|-------------|-----------|
| `ENCRYPT_DECRYPT` | Symmetric encryption | S3, RDS, EBS encryption |
| `SIGN_VERIFY` | Asymmetric signing | Document signing, JWT tokens |
| `GENERATE_VERIFY_MAC` | MAC generation | API authentication, data integrity |

### Key Specifications

#### Symmetric Keys (ENCRYPT_DECRYPT)
- `SYMMETRIC_DEFAULT` - AES-256-GCM (default)

#### Asymmetric Keys (SIGN_VERIFY)
- `RSA_2048` - RSA with 2048-bit key
- `RSA_3072` - RSA with 3072-bit key
- `RSA_4096` - RSA with 4096-bit key
- `ECC_NIST_P256` - NIST P-256 curve
- `ECC_NIST_P384` - NIST P-384 curve
- `ECC_NIST_P521` - NIST P-521 curve
- `ECC_SECG_P256K1` - SECG P-256k1 curve

#### MAC Keys (GENERATE_VERIFY_MAC)
- `HMAC_224` - HMAC with SHA-224
- `HMAC_256` - HMAC with SHA-256
- `HMAC_384` - HMAC with SHA-384
- `HMAC_512` - HMAC with SHA-512

## Outputs

### Primary Outputs

| Name | Description | Type |
|------|-------------|------|
| key_details | Complete key information | `map(object)` |
| key_arns | Map of key names to ARNs | `map(string)` |
| key_ids | Map of key names to IDs | `map(string)` |
| alias_names | Map of key names to alias names | `map(string)` |
| alias_arns | Map of key names to alias ARNs | `map(string)` |

### Grouped Outputs

| Name | Description | Type |
|------|-------------|------|
| encryption_keys | Encryption key ARNs | `map(string)` |
| signing_keys | Signing key ARNs | `map(string)` |
| mac_keys | MAC key ARNs | `map(string)` |

### Configuration Outputs

| Name | Description | Type |
|------|-------------|------|
| key_configuration | Application configuration values | `object` |
| grant_tokens | KMS grant tokens | `map(string)` |
| external_key_details | External key information | `map(object)` |
| replica_key_details | Replica key information | `map(object)` |

## Key Configuration

### Naming Behavior

The module supports flexible naming:

- **With name_prefix**: `name_prefix = "myapp"` + `name = "encryption"` → Key: `myapp-encryption`, Alias: `alias/myapp-encryption`
- **Without name_prefix**: `name = "my-app-encryption-key"` → Key: `my-app-encryption-key`, Alias: `alias/my-app-encryption-key`

Choose the approach that best fits your naming conventions and organizational needs.

### Cross-Account Access

```hcl
cross_account_access = [
  {
    principals = ["arn:aws:iam::************:root"]
    actions    = ["kms:Decrypt", "kms:GenerateDataKey"]
    conditions = [
      {
        test     = "StringEquals"
        variable = "kms:ViaService"
        values   = ["s3.us-west-2.amazonaws.com"]
      }
    ]
  }
]
```

### Service Access

```hcl
service_access = [
  {
    services = ["s3.amazonaws.com", "rds.amazonaws.com"]
    actions  = ["kms:Decrypt", "kms:GenerateDataKey", "kms:CreateGrant"]
  }
]
```

### KMS Grants

```hcl
grants = [
  {
    grantee_principal = "arn:aws:iam::************:role/MyRole"
    operations        = ["Decrypt", "GenerateDataKey"]
    constraints = {
      encryption_context_equals = {
        Department = "Finance"
      }
    }
  }
]
```

## Examples

### Example 1: Basic Encryption Keys with Prefix

```hcl
module "kms" {
  source = "sparrow-tofu-modules/components/aws/kms"

  name_prefix = "myapp-prod"

  keys = [
    {
      name        = "s3-encryption"
      description = "KMS key for S3 bucket encryption"
      
      # Service access for S3
      service_access = [
        {
          services = ["s3.amazonaws.com"]
          actions  = [
            "kms:Decrypt",
            "kms:GenerateDataKey"
          ]
        }
      ]
      
      tags = {
        Purpose = "s3-encryption"
      }
    },
    {
      name        = "rds-encryption"
      description = "KMS key for RDS encryption"
      
      # Service access for RDS
      service_access = [
        {
          services = ["rds.amazonaws.com"]
          actions  = [
            "kms:Decrypt",
            "kms:GenerateDataKey",
            "kms:CreateGrant"
          ]
        }
      ]
      
      tags = {
        Purpose = "rds-encryption"
      }
    }
  ]

  tags = {
    Environment = "production"
    Project     = "myapp"
    ManagedBy   = "terraform"
  }
}
```

**Result**: Creates keys named `myapp-prod-s3-encryption` and `myapp-prod-rds-encryption` with aliases `alias/myapp-prod-s3-encryption` and `alias/myapp-prod-rds-encryption`.

### Example 2: Simple Keys Without Prefix

```hcl
module "kms_simple" {
  source = "sparrow-tofu-modules/components/aws/kms"

  keys = [
    {
      name        = "application-encryption-key"
      description = "Main encryption key for application"
    },
    {
      name        = "backup-encryption-key"
      description = "Encryption key for backups"
      
      service_access = [
        {
          services = ["s3.amazonaws.com"]
          actions  = ["kms:Decrypt", "kms:GenerateDataKey"]
        }
      ]
    }
  ]

  tags = {
    Environment = "development"
    Team        = "backend"
  }
}
```

**Result**: Creates keys named `application-encryption-key` and `backup-encryption-key` with aliases `alias/application-encryption-key` and `alias/backup-encryption-key`.

## Security Considerations

### Default Security Features

1. **Key Rotation**: Enabled by default for encryption keys
2. **Deletion Protection**: 30-day deletion window by default
3. **Secure Policies**: Principle of least privilege in default policies
4. **Audit Trail**: All operations logged via CloudTrail

### Best Practices

1. **Use specific key purposes**: Create separate keys for different use cases
2. **Enable key rotation**: Keep automatic rotation enabled for encryption keys
3. **Implement least privilege**: Grant only necessary permissions
4. **Use encryption context**: Add additional security context where possible
5. **Monitor usage**: Set up CloudWatch alarms for key usage patterns

### Compliance

- **SOC 2**: KMS is SOC 2 compliant
- **PCI DSS**: Suitable for PCI DSS environments
- **HIPAA**: HIPAA eligible service
- **FedRAMP**: FedRAMP authorized

---

For more examples and usage patterns, see [USAGE.md](./USAGE.md).
