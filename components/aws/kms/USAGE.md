# KMS Module Usage Guide

This guide provides comprehensive examples and best practices for using the KMS module in your Terraform configurations.

## Table of Contents

- [Quick Start](#quick-start)
- [Common Use Cases](#common-use-cases)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)
- [Migration Guide](#migration-guide)

## Quick Start

### 1. Simple Encryption Key (No Prefix)

```hcl
module "kms" {
  source = "sparrow-tofu-modules/components/aws/kms"

  keys = [
    {
      name        = "my-app-encryption-key"
      description = "General purpose encryption key"
    }
  ]

  tags = {
    Environment = "development"
    Project     = "myapp"
  }
}
```

### 2. Organized Keys with Prefix

```hcl
module "kms" {
  source = "sparrow-tofu-modules/components/aws/kms"

  name_prefix = "myapp-dev"  # Optional: adds prefix to all key names

  keys = [
    {
      name        = "general-encryption"
      description = "General purpose encryption key"
    }
  ]

  tags = {
    Environment = "development"
    Project     = "myapp"
  }
}
```

### 3. When to Use Prefixes vs Simple Names

**Use Simple Names (no prefix) when:**
- Quick development or testing
- Single application or simple setup
- You want full control over naming

**Use Prefixes when:**
- Multiple environments (dev, staging, prod)
- Multiple applications sharing AWS account
- Organizational naming standards
- Team-based resource separation

```hcl
# Simple approach - direct naming
module "kms_simple" {
  source = "sparrow-tofu-modules/components/aws/kms"

  keys = [
    {
      name        = "user-data-encryption"
      description = "Encrypts user data in the application"
    },
    {
      name        = "backup-encryption"
      description = "Encrypts application backups"
    }
  ]
}

# Organized approach - with prefix
module "kms_organized" {
  source = "sparrow-tofu-modules/components/aws/kms"

  name_prefix = "ecommerce-prod"

  keys = [
    {
      name = "user-data"      # becomes: ecommerce-prod-user-data
    },
    {
      name = "backups"        # becomes: ecommerce-prod-backups
    }
  ]
}
```

## Common Use Cases

### Application Data Encryption

```hcl
module "app_encryption" {
  source = "sparrow-tofu-modules/components/aws/kms"

  name_prefix = "ecommerce-prod"

  keys = [
    {
      name        = "database-encryption"
      description = "KMS key for RDS database encryption"
      
      # Allow RDS service access
      service_access = [
        {
          services = ["rds.amazonaws.com"]
          actions  = [
            "kms:Decrypt",
            "kms:GenerateDataKey",
            "kms:CreateGrant",
            "kms:DescribeKey"
          ]
        }
      ]
      
      # Cross-account access for backup account
      cross_account_access = [
        {
          principals = ["arn:aws:iam::************:root"]
          actions    = ["kms:Decrypt", "kms:GenerateDataKey"]
          conditions = [
            {
              test     = "StringEquals"
              variable = "kms:ViaService"
              values   = ["rds.us-west-2.amazonaws.com"]
            }
          ]
        }
      ]
      
      tags = {
        Purpose     = "database-encryption"
        Criticality = "high"
      }
    },
    {
      name        = "s3-encryption"
      description = "KMS key for S3 bucket encryption"
      
      # S3 service access
      service_access = [
        {
          services = ["s3.amazonaws.com"]
          actions  = [
            "kms:Decrypt",
            "kms:GenerateDataKey",
            "kms:ReEncrypt*",
            "kms:DescribeKey"
          ]
        }
      ]
      
      # Application role access via grants
      grants = [
        {
          grantee_principal = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/EcommerceAppRole"
          operations        = ["Decrypt", "GenerateDataKey"]
          name             = "ApplicationAccess"
          constraints = {
            encryption_context_equals = {
              "application" = "ecommerce"
            }
          }
        }
      ]
      
      tags = {
        Purpose = "s3-encryption"
      }
    },
    {
      name        = "secrets-encryption"
      description = "KMS key for Secrets Manager encryption"
      
      # Secrets Manager service access
      service_access = [
        {
          services = ["secretsmanager.amazonaws.com"]
          actions  = [
            "kms:Decrypt",
            "kms:GenerateDataKey",
            "kms:ReEncrypt*",
            "kms:DescribeKey"
          ]
        }
      ]
      
      tags = {
        Purpose = "secrets-encryption"
      }
    }
  ]

  tags = {
    Application = "ecommerce"
    Environment = "production"
    Team        = "platform"
  }
}
```

### Multi-Region Global Application

```hcl
module "global_encryption" {
  source = "sparrow-tofu-modules/components/aws/kms"

  name_prefix = "global-saas"

  keys = [
    {
      name         = "global-app-key"
      description  = "Multi-region key for global SaaS application"
      multi_region = true
      
      # Replica in EU for GDPR compliance
      replica_regions = [
        {
          region                  = "eu-west-1"
          deletion_window_in_days = 30
          enabled                = true
          tags = {
            Region     = "europe"
            Compliance = "gdpr"
          }
        },
        {
          region = "ap-southeast-1"
          tags = {
            Region = "asia-pacific"
          }
        }
      ]
      
      # Cross-account access for partner integrations
      cross_account_access = [
        {
          principals = [
            "arn:aws:iam::************:root",  # Partner Account 1
            "arn:aws:iam::************:root"   # Partner Account 2
          ]
          actions = [
            "kms:Decrypt",
            "kms:GenerateDataKey"
          ]
          conditions = [
            {
              test     = "StringEquals"
              variable = "kms:EncryptionContext:partner"
              values   = ["authorized"]
            }
          ]
        }
      ]
      
      # Service access for multiple AWS services
      service_access = [
        {
          services = [
            "s3.amazonaws.com",
            "rds.amazonaws.com",
            "secretsmanager.amazonaws.com"
          ]
          actions = [
            "kms:Decrypt",
            "kms:GenerateDataKey",
            "kms:CreateGrant",
            "kms:DescribeKey"
          ]
        }
      ]
      
      tags = {
        Purpose = "global-encryption"
        Scope   = "multi-region"
      }
    }
  ]

  tags = {
    Application = "global-saas"
    Environment = "production"
    Compliance  = "sox-gdpr"
  }

  providers = {
    aws.replica = aws.eu_west_1
  }
}
```

### Document Signing and API Security

```hcl
module "security_keys" {
  source = "sparrow-tofu-modules/components/aws/kms"

  name_prefix = "fintech-security"

  keys = [
    {
      name                = "document-signing"
      description         = "RSA key for financial document signing"
      key_usage          = "SIGN_VERIFY"
      key_spec           = "RSA_3072"
      enable_key_rotation = false  # Signing keys cannot be rotated
      
      # Custom policy for document signing
      policy = jsonencode({
        Version = "2012-10-17"
        Statement = [
          {
            Sid    = "EnableIAMUserPermissions"
            Effect = "Allow"
            Principal = {
              AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
            }
            Action   = "kms:*"
            Resource = "*"
          },
          {
            Sid    = "AllowDocumentSigning"
            Effect = "Allow"
            Principal = {
              AWS = [
                "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/DocumentSigningRole",
                "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/ComplianceRole"
              ]
            }
            Action = [
              "kms:Sign",
              "kms:Verify",
              "kms:DescribeKey",
              "kms:GetPublicKey"
            ]
            Resource = "*"
            Condition = {
              StringEquals = {
                "kms:EncryptionContext:document-type" = [
                  "contract",
                  "financial-report",
                  "audit-document"
                ]
              }
            }
          }
        ]
      })
      
      # Grants for specific applications
      grants = [
        {
          grantee_principal = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/ContractSigningApp"
          operations        = ["Sign", "Verify", "DescribeKey"]
          name             = "ContractSigningAccess"
          constraints = {
            encryption_context_equals = {
              "document-type" = "contract"
            }
          }
        }
      ]
      
      tags = {
        Purpose     = "document-signing"
        KeyType     = "signing"
        Compliance  = "sox"
        Criticality = "high"
      }
    },
    {
      name                = "api-authentication"
      description         = "HMAC key for API request authentication"
      key_usage          = "GENERATE_VERIFY_MAC"
      key_spec           = "HMAC_256"
      enable_key_rotation = false  # MAC keys cannot be rotated
      
      # Grants for API Gateway and Lambda
      grants = [
        {
          grantee_principal = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/APIGatewayRole"
          operations        = ["GenerateMac", "VerifyMac", "DescribeKey"]
          name             = "APIGatewayAccess"
        },
        {
          grantee_principal = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/LambdaExecutionRole"
          operations        = ["GenerateMac", "VerifyMac", "DescribeKey"]
          name             = "LambdaAccess"
          constraints = {
            encryption_context_subset = {
              "api-version" = "v1"
            }
          }
        }
      ]
      
      tags = {
        Purpose = "api-authentication"
        KeyType = "mac"
      }
    },
    {
      name                = "jwt-signing"
      description         = "ECC key for JWT token signing"
      key_usage          = "SIGN_VERIFY"
      key_spec           = "ECC_NIST_P256"
      enable_key_rotation = false
      
      # Service access for Cognito
      service_access = [
        {
          services = ["cognito-idp.amazonaws.com"]
          actions  = ["kms:Sign", "kms:Verify", "kms:DescribeKey", "kms:GetPublicKey"]
        }
      ]
      
      tags = {
        Purpose = "jwt-signing"
        KeyType = "signing"
        Service = "cognito"
      }
    }
  ]

  tags = {
    Application = "fintech-platform"
    Environment = "production"
    Team        = "security"
    Compliance  = "pci-dss"
  }
}
```

### HSM Integration with External Keys

```hcl
module "hsm_keys" {
  source = "sparrow-tofu-modules/components/aws/kms"

  name_prefix = "banking-hsm"

  keys = [
    {
      name                = "hsm-master-key"
      description         = "Master key with material from HSM"
      key_material_base64 = var.hsm_key_material  # From HSM
      valid_to           = "2025-12-31T23:59:59Z"
      enable_key_rotation = false  # Cannot rotate external keys
      
      # Strict policy for HSM key
      policy = jsonencode({
        Version = "2012-10-17"
        Statement = [
          {
            Sid    = "EnableIAMUserPermissions"
            Effect = "Allow"
            Principal = {
              AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
            }
            Action   = "kms:*"
            Resource = "*"
          },
          {
            Sid    = "RestrictToHSMRole"
            Effect = "Allow"
            Principal = {
              AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/HSMRole"
            }
            Action = [
              "kms:Encrypt",
              "kms:Decrypt",
              "kms:ReEncrypt*",
              "kms:GenerateDataKey*",
              "kms:DescribeKey"
            ]
            Resource = "*"
            Condition = {
              StringEquals = {
                "kms:EncryptionContext:hsm-validated" = "true"
              }
            }
          },
          {
            Sid    = "DenyDirectAccess"
            Effect = "Deny"
            Principal = "*"
            Action = [
              "kms:Encrypt",
              "kms:Decrypt",
              "kms:ReEncrypt*",
              "kms:GenerateDataKey*"
            ]
            Resource = "*"
            Condition = {
              StringNotEquals = {
                "aws:PrincipalArn" = [
                  "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/HSMRole",
                  "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
                ]
              }
            }
          }
        ]
      })
      
      tags = {
        Purpose     = "hsm-master-key"
        KeySource   = "external-hsm"
        Compliance  = "fips-140-2-level-3"
        Criticality = "critical"
      }
    }
  ]

  tags = {
    Application = "core-banking"
    Environment = "production"
    Compliance  = "pci-dss-fips"
    Team        = "security"
  }
}
```

## Best Practices

### 1. Naming Best Practices

```hcl
# Good naming examples without prefix
module "kms_good_naming" {
  source = "sparrow-tofu-modules/components/aws/kms"

  keys = [
    # Descriptive and clear purpose
    { name = "user-data-encryption-key" },
    { name = "payment-processing-key" },
    { name = "audit-log-encryption" },

    # Include environment in name when not using prefix
    { name = "prod-database-encryption" },
    { name = "staging-api-signing-key" }
  ]
}

# Good naming examples with prefix
module "kms_with_prefix" {
  source = "sparrow-tofu-modules/components/aws/kms"

  name_prefix = "ecommerce-prod"

  keys = [
    # Short, clear names since prefix provides context
    { name = "database" },      # becomes: ecommerce-prod-database
    { name = "s3-storage" },    # becomes: ecommerce-prod-s3-storage
    { name = "api-signing" }    # becomes: ecommerce-prod-api-signing
  ]
}

# Avoid these naming patterns
module "kms_bad_naming" {
  source = "sparrow-tofu-modules/components/aws/kms"

  keys = [
    # Too generic
    { name = "key1" },
    { name = "encryption" },

    # Too long and redundant
    { name = "my-application-encryption-key-for-database-encryption" },

    # Unclear purpose
    { name = "temp-key" },
    { name = "test" }
  ]
}
```

### 2. Environment-Specific Configuration

```hcl
locals {
  environment_config = {
    development = {
      key_rotation        = false
      deletion_window     = 7
      multi_region       = false
      cross_account_access = []
      use_prefix         = false  # Simple naming for dev
    }
    staging = {
      key_rotation        = true
      deletion_window     = 15
      multi_region       = false
      cross_account_access = []
      use_prefix         = true   # Organized naming for staging
    }
    production = {
      key_rotation        = true
      deletion_window     = 30
      multi_region       = true
      cross_account_access = var.production_cross_account_access
      use_prefix         = true   # Organized naming for production
    }
  }
}

# Development - simple naming
module "kms_dev" {
  count  = var.environment == "development" ? 1 : 0
  source = "sparrow-tofu-modules/components/aws/kms"

  keys = [
    {
      name                    = "dev-app-encryption-key"
      enable_key_rotation     = local.environment_config[var.environment].key_rotation
      deletion_window_in_days = local.environment_config[var.environment].deletion_window
    }
  ]

  tags = {
    Environment = var.environment
  }
}

# Staging/Production - organized naming
module "kms_organized" {
  count  = var.environment != "development" ? 1 : 0
  source = "sparrow-tofu-modules/components/aws/kms"

  name_prefix = "${var.environment}-myapp"

  keys = [
    {
      name                    = "app-encryption"
      enable_key_rotation     = local.environment_config[var.environment].key_rotation
      deletion_window_in_days = local.environment_config[var.environment].deletion_window
      multi_region           = local.environment_config[var.environment].multi_region
      cross_account_access   = local.environment_config[var.environment].cross_account_access
    }
  ]

  tags = {
    Environment = var.environment
  }
}
```

### 2. Using Outputs in Other Modules

```hcl
# Create KMS keys first
module "kms" {
  source = "sparrow-tofu-modules/components/aws/kms"
  
  name_prefix = "myapp"
  keys = [
    { name = "s3-encryption" },
    { name = "rds-encryption" },
    { name = "secrets-encryption" }
  ]
}

# Use in S3 module
module "s3" {
  source = "sparrow-tofu-modules/components/aws/s3"
  
  name_prefix = "myapp"
  buckets = [
    {
      name        = "secure-data"
      kms_key_arn = module.kms.key_arns["s3-encryption"]
    }
  ]
}

# Use in RDS
resource "aws_db_instance" "main" {
  # ... other configuration
  kms_key_id = module.kms.key_arns["rds-encryption"]
  encrypted  = true
}

# Use in Secrets Manager
resource "aws_secretsmanager_secret" "app_secrets" {
  name       = "myapp/secrets"
  kms_key_id = module.kms.key_arns["secrets-encryption"]
}
```

### 3. Application Configuration

```hcl
# Store key information in SSM for applications
resource "aws_ssm_parameter" "kms_keys" {
  for_each = module.kms.key_arns

  name  = "/app/kms/${each.key}/arn"
  type  = "String"
  value = each.value

  tags = var.tags
}

# Output for application configuration
output "app_config" {
  value = {
    kms_keys = module.kms.key_configuration
    aliases  = module.kms.alias_names
  }
}
```

## Troubleshooting

### Common Issues

1. **Key Policy Validation Errors**
   ```bash
   Error: Invalid key policy
   ```
   - Check JSON syntax and formatting
   - Validate principal ARNs exist
   - Ensure proper condition syntax

2. **Grant Creation Failures**
   ```bash
   Error: Cannot create grant
   ```
   - Verify grantee principal exists and is accessible
   - Check that operations are valid for key usage type
   - Validate constraint syntax

3. **Multi-Region Key Issues**
   ```bash
   Error: Cannot create replica key
   ```
   - Ensure replica regions are valid AWS regions
   - Check that provider aliases are configured
   - Verify cross-region permissions

4. **External Key Material Problems**
   ```bash
   Error: Invalid key material
   ```
   - Ensure key material is properly base64 encoded
   - Check that expiration date is in the future
   - Validate key material length (256 bits for AES)

### Validation Errors

The module includes comprehensive validation. Common validation errors:

- **Invalid key usage**: Must be ENCRYPT_DECRYPT, SIGN_VERIFY, or GENERATE_VERIFY_MAC
- **Invalid key spec**: Must be a valid AWS KMS key specification
- **Invalid deletion window**: Must be between 7 and 30 days
- **Key rotation conflicts**: Cannot enable rotation for signing/MAC keys or external keys
- **Multi-region constraints**: Multi-region keys must use SYMMETRIC_DEFAULT spec

## Migration Guide

### From Single Key Resources

```hcl
# Old individual key resources
resource "aws_kms_key" "app_key" {
  description = "Application encryption key"
  # ... other configuration
}

resource "aws_kms_alias" "app_key" {
  name          = "alias/myapp-encryption"
  target_key_id = aws_kms_key.app_key.key_id
}

# New module usage
module "kms" {
  source = "sparrow-tofu-modules/components/aws/kms"
  
  name_prefix = "myapp"
  keys = [
    {
      name        = "encryption"  # Will become alias/myapp-encryption
      description = "Application encryption key"
      # ... other configuration
    }
  ]
}
```

## Support

For issues with the KMS module:
1. Check the validation errors in the plan output
2. Verify your AWS permissions for KMS operations
3. Review the AWS KMS documentation for feature limitations
4. Check the module source code for advanced customization options
5. Ensure proper provider configuration for multi-region keys
