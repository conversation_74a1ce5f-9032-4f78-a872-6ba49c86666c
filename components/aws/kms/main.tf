# ------------------------------------------------------------------------------
# KMS Terraform Module
# This module creates KMS keys with comprehensive configurations and security best practices
# ------------------------------------------------------------------------------

terraform {
  required_providers {
    aws = {
      source = "hashicorp/aws"
    }
  }
}

# ------------------------------------------------------------------------------
# Local Values
# ------------------------------------------------------------------------------

locals {
  # Create key names with optional prefix
  key_names = {
    for key in var.keys : key.name => var.name_prefix != "" ? "${var.name_prefix}-${key.name}" : key.name
  }
}

# ------------------------------------------------------------------------------
# KMS Keys
# ------------------------------------------------------------------------------

resource "aws_kms_key" "this" {
  for_each = { for idx, key in var.keys : key.name => key }

  description              = lookup(each.value, "description", "KMS key for ${local.key_names[each.value.name]}")
  key_usage                = lookup(each.value, "key_usage", "ENCRYPT_DECRYPT")
  customer_master_key_spec = lookup(each.value, "key_spec", "SYMMETRIC_DEFAULT")

  # Key policy
  policy = lookup(each.value, "policy", null) != null ? each.value.policy : data.aws_iam_policy_document.default_key_policy[each.key].json

  # Deletion configuration
  deletion_window_in_days = lookup(each.value, "deletion_window_in_days", 30)
  is_enabled              = lookup(each.value, "enabled", true)

  # Rotation configuration
  enable_key_rotation = lookup(each.value, "enable_key_rotation", true)

  # Multi-region configuration
  multi_region = lookup(each.value, "multi_region", false)

  tags = merge(
    var.tags,
    lookup(each.value, "tags", {}),
    {
      Name      = local.key_names[each.value.name]
      ManagedBy = "terraform"
      Module    = "kms"
    }
  )
}

# ------------------------------------------------------------------------------
# Data Sources
# ------------------------------------------------------------------------------

# Get current AWS account ID
data "aws_caller_identity" "current" {}

# ------------------------------------------------------------------------------
# IAM Policy Documents
# ------------------------------------------------------------------------------

# Default key policy for keys without custom policy
data "aws_iam_policy_document" "default_key_policy" {
  for_each = { for k, key in var.keys : k => key if lookup(key, "policy", null) == null }

  statement {
    sid    = "EnableIAMUserPermissions"
    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"]
    }

    actions   = ["kms:*"]
    resources = ["*"]
  }

  # Additional statements for cross-account access
  dynamic "statement" {
    for_each = lookup(var.keys[each.key], "cross_account_access", [])
    content {
      sid    = "CrossAccountAccess${statement.key}"
      effect = "Allow"

      principals {
        type        = "AWS"
        identifiers = statement.value.principals
      }

      actions   = statement.value.actions
      resources = ["*"]

      dynamic "condition" {
        for_each = lookup(statement.value, "conditions", [])
        content {
          test     = condition.value.test
          variable = condition.value.variable
          values   = condition.value.values
        }
      }
    }
  }

  # Service access statements
  dynamic "statement" {
    for_each = lookup(var.keys[each.key], "service_access", [])
    content {
      sid    = "ServiceAccess${statement.key}"
      effect = "Allow"

      principals {
        type        = "Service"
        identifiers = statement.value.services
      }

      actions   = statement.value.actions
      resources = ["*"]

      dynamic "condition" {
        for_each = lookup(statement.value, "conditions", [])
        content {
          test     = condition.value.test
          variable = condition.value.variable
          values   = condition.value.values
        }
      }
    }
  }
}

# ------------------------------------------------------------------------------
# KMS Aliases
# ------------------------------------------------------------------------------

resource "aws_kms_alias" "this" {
  for_each = { for k, key in var.keys : k => key if lookup(key, "create_alias", true) }

  name          = "alias/${local.key_names[each.value.name]}"
  target_key_id = aws_kms_key.this[each.key].key_id
}

# ------------------------------------------------------------------------------
# KMS Grants
# ------------------------------------------------------------------------------

resource "aws_kms_grant" "this" {
  for_each = {
    for grant_key, grant in local.all_grants : grant_key => grant
  }

  key_id            = aws_kms_key.this[each.value.key_name].key_id
  grantee_principal = each.value.grantee_principal
  operations        = each.value.operations

  name               = lookup(each.value, "name", null)
  retiring_principal = lookup(each.value, "retiring_principal", null)

  dynamic "constraints" {
    for_each = lookup(each.value, "constraints", null) != null ? [each.value.constraints] : []
    content {
      encryption_context_equals = lookup(constraints.value, "encryption_context_equals", null)
      encryption_context_subset = lookup(constraints.value, "encryption_context_subset", null)
    }
  }
}

# Local values for processing grants
locals {
  all_grants = merge([
    for key_name, key in var.keys : {
      for grant_idx, grant in lookup(key, "grants", []) :
      "${key_name}-grant-${grant_idx}" => merge(grant, {
        key_name = key_name
      })
    }
  ]...)
}

# Create external key for imported key material (if specified)
resource "aws_kms_external_key" "this" {
  for_each = { for k, key in var.keys : k => key if lookup(key, "key_material_base64", null) != null }

  description             = lookup(each.value, "description", "External KMS key for ${local.key_names[each.value.name]}")
  deletion_window_in_days = lookup(each.value, "deletion_window_in_days", 30)
  enabled                 = lookup(each.value, "enabled", true)

  # Key material
  key_material_base64 = each.value.key_material_base64
  valid_to            = lookup(each.value, "valid_to", null)

  policy = lookup(each.value, "policy", null) != null ? each.value.policy : data.aws_iam_policy_document.default_key_policy[each.key].json

  tags = merge(
    var.tags,
    lookup(each.value, "tags", {}),
    {
      Name      = "${local.key_names[each.value.name]}-external"
      ManagedBy = "terraform"
      Module    = "kms"
    }
  )
}

# Create aliases for external keys
resource "aws_kms_alias" "external" {
  for_each = { for k, key in var.keys : k => key if lookup(key, "key_material_base64", null) != null && lookup(key, "create_alias", true) }

  name          = "alias/${local.key_names[each.value.name]}-external"
  target_key_id = aws_kms_external_key.this[each.key].key_id
}

# Create replica keys for multi-region keys
resource "aws_kms_replica_key" "this" {
  for_each = {
    for replica_key, replica in local.all_replicas : replica_key => replica
  }

  primary_key_arn         = aws_kms_key.this[each.value.key_name].arn
  description             = "Replica of ${local.key_names[each.value.key_name]} in ${each.value.region}"
  deletion_window_in_days = lookup(each.value, "deletion_window_in_days", 30)
  enabled                 = lookup(each.value, "enabled", true)

  policy = lookup(each.value, "policy", null)

  tags = merge(
    var.tags,
    lookup(each.value, "tags", {}),
    {
      Name      = "${local.key_names[each.value.key_name]}-replica-${each.value.region}"
      ManagedBy = "terraform"
      Module    = "kms"
      Region    = each.value.region
    }
  )

  provider = aws.replica
}

# Local values for processing replica keys
locals {
  all_replicas = merge([
    for key_name, key in var.keys : {
      for replica_idx, replica in lookup(key, "replica_regions", []) :
      "${key_name}-replica-${replica.region}" => merge(replica, {
        key_name = key_name
      })
    } if lookup(key, "multi_region", false)
  ]...)
}
