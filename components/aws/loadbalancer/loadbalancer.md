# AWS Load Balancer Terraform Module

This module creates and manages AWS Application Load Balancers (ALB) and Network Load Balancers (NLB) with comprehensive configurations, target groups, listeners, and security best practices.

## Features

- **Multi-Load Balancer Support**: Create multiple load balancers in a single module call
- **ALB and NLB Support**: Full support for both Application and Network Load Balancers
- **Flexible Naming**: Optional prefix for organized resource naming
- **Comprehensive Configuration**: Support for all load balancer features and settings
- **Target Group Management**: Automatic target group creation and attachment
- **Listener Configuration**: Support for multiple listeners with various actions
- **Security Best Practices**: Secure defaults and comprehensive validation
- **Rich Outputs**: Detailed outputs for integration with other resources

## Supported Load Balancer Types

### Application Load Balancer (ALB)
- HTTP/HTTPS traffic routing
- Content-based routing
- SSL/TLS termination
- WebSocket support
- HTTP/2 support
- Authentication integration (Cognito, OIDC)
- WAF integration

### Network Load Balancer (NLB)
- TCP/UDP/TLS traffic routing
- Ultra-high performance
- Static IP addresses
- Preserve source IP
- Connection-based routing

## Resource Architecture

The module creates the following AWS resources:

1. **Load Balancers** (`aws_lb`)
   - Application or Network Load Balancers
   - Configurable schemes (internet-facing/internal)
   - Access logging and connection logging support

2. **Target Groups** (`aws_lb_target_group`)
   - Health check configuration
   - Stickiness settings
   - Load balancing algorithms
   - Target attachments

3. **Listeners** (`aws_lb_listener`)
   - Protocol and port configuration
   - SSL/TLS settings
   - Default actions (forward, redirect, fixed-response, auth)

4. **Target Group Attachments** (`aws_lb_target_group_attachment`)
   - Instance, IP, Lambda, or ALB targets
   - Port and availability zone configuration

## Key Features

### Flexible Naming Strategy
- **With Prefix**: `myapp-prod-web` → `myapp-prod-web-lb`
- **Without Prefix**: `web-load-balancer` → `web-load-balancer`

### Security Features
- Encryption in transit with SSL/TLS
- Security group integration for ALBs
- Access logging for audit trails
- Connection logging for NLBs
- Deletion protection option

### High Availability
- Multi-AZ deployment support
- Cross-zone load balancing
- Health check configuration
- Auto-scaling integration

### Advanced Routing
- Path-based routing (ALB)
- Host-based routing (ALB)
- Weighted routing
- Sticky sessions
- Authentication actions

## Usage Examples

### Example 1: Simple Application Load Balancer

```hcl
module "alb" {
  source = "sparrow-tofu-modules/components/aws/loadbalancer"

  name_prefix = "myapp-prod"

  load_balancers = [
    {
      name               = "web"
      load_balancer_type = "application"
      scheme             = "internet-facing"
      subnets            = ["subnet-12345", "subnet-67890"]
      security_groups    = ["sg-12345"]

      target_groups = [
        {
          name     = "web-servers"
          port     = 80
          protocol = "HTTP"
          vpc_id   = "vpc-12345"
          
          health_check = {
            path                = "/health"
            healthy_threshold   = 2
            unhealthy_threshold = 2
          }
          
          targets = [
            { id = "i-1234567890abcdef0" },
            { id = "i-0987654321fedcba0" }
          ]
        }
      ]

      listeners = [
        {
          port     = 80
          protocol = "HTTP"
          default_action = {
            type             = "forward"
            target_group_arn = "web-servers"  # Will be resolved automatically
          }
        }
      ]
    }
  ]

  tags = {
    Environment = "production"
    Project     = "myapp"
    ManagedBy   = "terraform"
  }
}
```

### Example 2: Network Load Balancer with TCP

```hcl
module "nlb" {
  source = "sparrow-tofu-modules/components/aws/loadbalancer"

  name_prefix = "database"

  load_balancers = [
    {
      name               = "db-proxy"
      load_balancer_type = "network"
      scheme             = "internal"
      subnets            = ["subnet-12345", "subnet-67890"]
      
      enable_cross_zone_load_balancing = true

      target_groups = [
        {
          name        = "db-instances"
          port        = 5432
          protocol    = "TCP"
          target_type = "instance"
          vpc_id      = "vpc-12345"
          
          health_check = {
            protocol = "TCP"
            port     = "5432"
            interval = 10
          }
          
          targets = [
            { id = "i-db1234567890", port = 5432 },
            { id = "i-db0987654321", port = 5432 }
          ]
        }
      ]

      listeners = [
        {
          port     = 5432
          protocol = "TCP"
          default_action = {
            type             = "forward"
            target_group_arn = "db-instances"
          }
        }
      ]
    }
  ]

  tags = {
    Environment = "production"
    Service     = "database"
  }
}
```

## Input Variables

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| `name_prefix` | A prefix for all resource names | `string` | `""` | no |
| `tags` | A map of tags to assign to all resources | `map(string)` | `{}` | no |
| `load_balancers` | List of load balancers to create | `list(object)` | n/a | yes |

### Load Balancer Configuration

Each load balancer supports the following configuration:

- **Basic Settings**: name, type, scheme, IP address type
- **Network**: subnets, security groups (ALB only)
- **Features**: deletion protection, cross-zone load balancing, HTTP/2
- **Logging**: access logs, connection logs (NLB only)
- **Target Groups**: health checks, stickiness, load balancing algorithms
- **Listeners**: protocols, SSL policies, default actions

## Outputs

The module provides comprehensive outputs for integration:

- **Load Balancer Details**: ARNs, DNS names, zone IDs
- **Target Group Information**: ARNs, names, configurations
- **Listener Details**: ARNs, protocols, ports
- **Application Configuration**: Ready-to-use configuration values
- **Convenience Outputs**: Filtered by type, scheme, etc.

## Validation

The module includes comprehensive validation:

- Load balancer names must be unique and within AWS limits
- Load balancer types must be valid (application/network)
- Schemes must be valid (internet-facing/internal)
- ALBs must have security groups
- Minimum subnet requirements
- Protocol and port validations

## Security Considerations

1. **Network Security**: Use appropriate security groups for ALBs
2. **Encryption**: Enable SSL/TLS for sensitive traffic
3. **Access Logging**: Enable for audit and compliance
4. **Deletion Protection**: Enable for production load balancers
5. **Health Checks**: Configure appropriate health check paths
6. **Authentication**: Use Cognito or OIDC for user authentication

## Best Practices

1. **High Availability**: Deploy across multiple AZs
2. **Health Checks**: Configure meaningful health check endpoints
3. **SSL/TLS**: Use ACM certificates for SSL termination
4. **Monitoring**: Enable access logs and CloudWatch metrics
5. **Tagging**: Use consistent tagging for resource management
6. **Target Groups**: Group targets logically by function
7. **Listeners**: Use appropriate protocols for your use case

## Integration Examples

### With Auto Scaling Groups

```hcl
resource "aws_autoscaling_attachment" "web" {
  autoscaling_group_name = aws_autoscaling_group.web.id
  lb_target_group_arn    = module.alb.target_group_arns["web-web-servers"]
}
```

### With Route 53

```hcl
resource "aws_route53_record" "web" {
  zone_id = data.aws_route53_zone.main.zone_id
  name    = "web.example.com"
  type    = "A"

  alias {
    name                   = module.alb.load_balancer_dns_names["web"]
    zone_id                = module.alb.load_balancer_zone_ids["web"]
    evaluate_target_health = true
  }
}
```

## Troubleshooting

### Common Issues

1. **Target Health**: Check security groups and health check configuration
2. **SSL Issues**: Verify certificate ARN and SSL policy
3. **Routing**: Ensure listener rules and target group configuration
4. **Permissions**: Verify IAM permissions for load balancer operations

### Debugging

- Use AWS Console to check target health
- Review CloudWatch logs and metrics
- Verify security group rules
- Check subnet routing tables

## Migration and Upgrades

The module supports:
- Adding new load balancers to existing configurations
- Modifying target group settings
- Adding or removing listeners
- Updating SSL certificates

Always test changes in a non-production environment first.

## Support

For issues with the Load Balancer module:
1. Check AWS service limits and quotas
2. Verify network configuration and security groups
3. Review AWS Load Balancer documentation
4. Check module validation errors in Terraform plan output
