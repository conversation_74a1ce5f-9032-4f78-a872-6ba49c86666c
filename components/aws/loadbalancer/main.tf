# ------------------------------------------------------------------------------
# Load Balancer Terraform Module
# This module creates Application and Network Load Balancers with comprehensive configurations and security best practices
# ------------------------------------------------------------------------------

terraform {
  required_providers {
    aws = {
      source = "hashicorp/aws"
    }
  }
}

# ------------------------------------------------------------------------------
# Local Values
# ------------------------------------------------------------------------------

locals {
  # Create load balancer names with optional prefix
  load_balancer_names = {
    for lb in var.load_balancers : lb.name => var.name_prefix != "" ? "${var.name_prefix}-${lb.name}" : lb.name
  }
  
  # Create a map for easier resource creation
  load_balancer_map = {
    for lb in var.load_balancers : lb.name => lb
  }
  
  # Flatten target groups for easier iteration
  target_groups = flatten([
    for lb_name, lb in local.load_balancer_map : [
      for tg in lb.target_groups : {
        lb_name = lb_name
        tg_name = tg.name
        tg_key  = "${lb_name}-${tg.name}"
        config  = tg
      }
    ]
  ])
  
  # Create target group map
  target_group_map = {
    for tg in local.target_groups : tg.tg_key => tg
  }
  
  # Flatten listeners for easier iteration
  listeners = flatten([
    for lb_name, lb in local.load_balancer_map : [
      for idx, listener in lb.listeners : {
        lb_name      = lb_name
        listener_key = "${lb_name}-${idx}"
        config       = listener
      }
    ]
  ])
  
  # Create listener map
  listener_map = {
    for listener in local.listeners : listener.listener_key => listener
  }
  
  # Flatten targets for easier iteration
  targets = flatten([
    for tg_key, tg in local.target_group_map : [
      for target in tg.config.targets : {
        tg_key     = tg_key
        target_key = "${tg_key}-${target.id}"
        config     = target
      }
    ]
  ])
  
  # Create target map
  target_map = {
    for target in local.targets : target.target_key => target
  }
}

# ------------------------------------------------------------------------------
# Load Balancers
# ------------------------------------------------------------------------------

resource "aws_lb" "this" {
  for_each = local.load_balancer_map

  name               = local.load_balancer_names[each.key]
  load_balancer_type = each.value.load_balancer_type
  scheme             = each.value.scheme
  ip_address_type    = each.value.ip_address_type
  
  subnets         = each.value.subnets
  security_groups = each.value.load_balancer_type == "application" ? each.value.security_groups : null
  
  enable_deletion_protection       = each.value.enable_deletion_protection
  enable_cross_zone_load_balancing = each.value.enable_cross_zone_load_balancing
  enable_http2                     = each.value.load_balancer_type == "application" ? each.value.enable_http2 : null
  enable_waf_fail_open            = each.value.load_balancer_type == "application" ? each.value.enable_waf_fail_open : null
  
  idle_timeout = each.value.load_balancer_type == "application" ? each.value.idle_timeout : null

  dynamic "access_logs" {
    for_each = each.value.access_logs != null ? [each.value.access_logs] : []
    content {
      enabled = access_logs.value.enabled
      bucket  = access_logs.value.bucket
      prefix  = access_logs.value.prefix
    }
  }

  dynamic "connection_logs" {
    for_each = each.value.connection_logs != null && each.value.load_balancer_type == "network" ? [each.value.connection_logs] : []
    content {
      enabled = connection_logs.value.enabled
      bucket  = connection_logs.value.bucket
      prefix  = connection_logs.value.prefix
    }
  }

  tags = merge(var.tags, each.value.tags, {
    Name = local.load_balancer_names[each.key]
  })
}

# ------------------------------------------------------------------------------
# Target Groups
# ------------------------------------------------------------------------------

resource "aws_lb_target_group" "this" {
  for_each = local.target_group_map

  name        = var.name_prefix != "" ? "${var.name_prefix}-${each.value.lb_name}-${each.value.config.name}" : "${each.value.lb_name}-${each.value.config.name}"
  port        = each.value.config.port
  protocol    = each.value.config.protocol
  target_type = each.value.config.target_type
  vpc_id      = each.value.config.vpc_id

  deregistration_delay              = each.value.config.deregistration_delay
  slow_start                       = each.value.config.slow_start
  load_balancing_algorithm_type    = each.value.config.load_balancing_algorithm_type
  preserve_client_ip               = each.value.config.preserve_client_ip
  proxy_protocol_v2                = each.value.config.proxy_protocol_v2

  dynamic "health_check" {
    for_each = each.value.config.health_check != null ? [each.value.config.health_check] : []
    content {
      enabled             = health_check.value.enabled
      healthy_threshold   = health_check.value.healthy_threshold
      unhealthy_threshold = health_check.value.unhealthy_threshold
      timeout             = health_check.value.timeout
      interval            = health_check.value.interval
      path                = health_check.value.path
      matcher             = health_check.value.matcher
      port                = health_check.value.port
      protocol            = health_check.value.protocol
    }
  }

  dynamic "stickiness" {
    for_each = each.value.config.stickiness != null ? [each.value.config.stickiness] : []
    content {
      enabled         = stickiness.value.enabled
      type            = stickiness.value.type
      cookie_duration = stickiness.value.cookie_duration
      cookie_name     = stickiness.value.cookie_name
    }
  }

  tags = merge(var.tags, each.value.config.tags, {
    Name = var.name_prefix != "" ? "${var.name_prefix}-${each.value.lb_name}-${each.value.config.name}" : "${each.value.lb_name}-${each.value.config.name}"
  })

  lifecycle {
    create_before_destroy = true
  }
}

# ------------------------------------------------------------------------------
# Target Group Attachments
# ------------------------------------------------------------------------------

resource "aws_lb_target_group_attachment" "this" {
  for_each = local.target_map

  target_group_arn  = aws_lb_target_group.this[each.value.tg_key].arn
  target_id         = each.value.config.id
  port              = each.value.config.port
  availability_zone = each.value.config.availability_zone
}

# ------------------------------------------------------------------------------
# Listeners
# ------------------------------------------------------------------------------

resource "aws_lb_listener" "this" {
  for_each = local.listener_map

  load_balancer_arn = aws_lb.this[each.value.lb_name].arn
  port              = each.value.config.port
  protocol          = each.value.config.protocol
  ssl_policy        = each.value.config.ssl_policy
  certificate_arn   = each.value.config.certificate_arn
  alpn_policy       = each.value.config.alpn_policy

  dynamic "default_action" {
    for_each = [each.value.config.default_action]
    content {
      type             = default_action.value.type
      target_group_arn = default_action.value.target_group_arn

      dynamic "forward" {
        for_each = default_action.value.forward != null ? [default_action.value.forward] : []
        content {
          dynamic "target_group" {
            for_each = forward.value.target_groups
            content {
              arn    = target_group.value.arn
              weight = target_group.value.weight
            }
          }
          
          dynamic "stickiness" {
            for_each = forward.value.stickiness != null ? [forward.value.stickiness] : []
            content {
              enabled  = stickiness.value.enabled
              duration = stickiness.value.duration
            }
          }
        }
      }

      dynamic "redirect" {
        for_each = default_action.value.redirect != null ? [default_action.value.redirect] : []
        content {
          host        = redirect.value.host
          path        = redirect.value.path
          port        = redirect.value.port
          protocol    = redirect.value.protocol
          query       = redirect.value.query
          status_code = redirect.value.status_code
        }
      }

      dynamic "fixed_response" {
        for_each = default_action.value.fixed_response != null ? [default_action.value.fixed_response] : []
        content {
          content_type = fixed_response.value.content_type
          message_body = fixed_response.value.message_body
          status_code  = fixed_response.value.status_code
        }
      }

      dynamic "authenticate_cognito" {
        for_each = default_action.value.authenticate_cognito != null ? [default_action.value.authenticate_cognito] : []
        content {
          user_pool_arn                       = authenticate_cognito.value.user_pool_arn
          user_pool_client_id                 = authenticate_cognito.value.user_pool_client_id
          user_pool_domain                    = authenticate_cognito.value.user_pool_domain
          authentication_request_extra_params = authenticate_cognito.value.authentication_request_extra_params
          on_unauthenticated_request          = authenticate_cognito.value.on_unauthenticated_request
          scope                               = authenticate_cognito.value.scope
          session_cookie_name                 = authenticate_cognito.value.session_cookie_name
          session_timeout                     = authenticate_cognito.value.session_timeout
        }
      }

      dynamic "authenticate_oidc" {
        for_each = default_action.value.authenticate_oidc != null ? [default_action.value.authenticate_oidc] : []
        content {
          authorization_endpoint              = authenticate_oidc.value.authorization_endpoint
          client_id                          = authenticate_oidc.value.client_id
          client_secret                      = authenticate_oidc.value.client_secret
          issuer                             = authenticate_oidc.value.issuer
          token_endpoint                     = authenticate_oidc.value.token_endpoint
          user_info_endpoint                 = authenticate_oidc.value.user_info_endpoint
          authentication_request_extra_params = authenticate_oidc.value.authentication_request_extra_params
          on_unauthenticated_request          = authenticate_oidc.value.on_unauthenticated_request
          scope                               = authenticate_oidc.value.scope
          session_cookie_name                 = authenticate_oidc.value.session_cookie_name
          session_timeout                     = authenticate_oidc.value.session_timeout
        }
      }
    }
  }

  tags = merge(var.tags, each.value.config.tags, {
    Name = var.name_prefix != "" ? "${var.name_prefix}-${each.value.lb_name}-listener-${each.value.config.port}" : "${each.value.lb_name}-listener-${each.value.config.port}"
  })
}
