# sparrow-tofu-modules/components/aws/loadbalancer/outputs.tf

output "load_balancer_details" {
  description = "Details of all created load balancers."
  value = {
    for k, lb in aws_lb.this : k => {
      id                               = lb.id
      arn                             = lb.arn
      arn_suffix                      = lb.arn_suffix
      dns_name                        = lb.dns_name
      zone_id                         = lb.zone_id
      name                            = lb.name
      load_balancer_type              = lb.load_balancer_type
      scheme                          = lb.scheme
      ip_address_type                 = lb.ip_address_type
      vpc_id                          = lb.vpc_id
      subnets                         = lb.subnets
      security_groups                 = lb.security_groups
      enable_deletion_protection      = lb.enable_deletion_protection
      enable_cross_zone_load_balancing = lb.enable_cross_zone_load_balancing
      enable_http2                    = lb.enable_http2
      enable_waf_fail_open           = lb.enable_waf_fail_open
      idle_timeout                    = lb.idle_timeout
      tags                            = lb.tags
    }
  }
}

output "load_balancer_names" {
  description = "Map of load balancer keys to their full names (including prefix)."
  value       = { for k, lb in aws_lb.this : k => lb.name }
}

output "load_balancer_arns" {
  description = "Map of load balancer keys to their ARNs."
  value       = { for k, lb in aws_lb.this : k => lb.arn }
}

output "load_balancer_dns_names" {
  description = "Map of load balancer keys to their DNS names."
  value       = { for k, lb in aws_lb.this : k => lb.dns_name }
}

output "load_balancer_zone_ids" {
  description = "Map of load balancer keys to their hosted zone IDs."
  value       = { for k, lb in aws_lb.this : k => lb.zone_id }
}

output "load_balancer_arn_suffixes" {
  description = "Map of load balancer keys to their ARN suffixes."
  value       = { for k, lb in aws_lb.this : k => lb.arn_suffix }
}

output "target_group_details" {
  description = "Details of all created target groups."
  value = {
    for k, tg in aws_lb_target_group.this : k => {
      id                               = tg.id
      arn                             = tg.arn
      arn_suffix                      = tg.arn_suffix
      name                            = tg.name
      port                            = tg.port
      protocol                        = tg.protocol
      target_type                     = tg.target_type
      vpc_id                          = tg.vpc_id
      deregistration_delay            = tg.deregistration_delay
      slow_start                      = tg.slow_start
      load_balancing_algorithm_type   = tg.load_balancing_algorithm_type
      preserve_client_ip              = tg.preserve_client_ip
      proxy_protocol_v2               = tg.proxy_protocol_v2
      health_check                    = tg.health_check
      stickiness                      = tg.stickiness
      tags                            = tg.tags
    }
  }
}

output "target_group_names" {
  description = "Map of target group keys to their names."
  value       = { for k, tg in aws_lb_target_group.this : k => tg.name }
}

output "target_group_arns" {
  description = "Map of target group keys to their ARNs."
  value       = { for k, tg in aws_lb_target_group.this : k => tg.arn }
}

output "target_group_arn_suffixes" {
  description = "Map of target group keys to their ARN suffixes."
  value       = { for k, tg in aws_lb_target_group.this : k => tg.arn_suffix }
}

output "listener_details" {
  description = "Details of all created listeners."
  value = {
    for k, listener in aws_lb_listener.this : k => {
      id               = listener.id
      arn              = listener.arn
      load_balancer_arn = listener.load_balancer_arn
      port             = listener.port
      protocol         = listener.protocol
      ssl_policy       = listener.ssl_policy
      certificate_arn  = listener.certificate_arn
      alpn_policy      = listener.alpn_policy
      default_action   = listener.default_action
      tags             = listener.tags
    }
  }
}

output "listener_arns" {
  description = "Map of listener keys to their ARNs."
  value       = { for k, listener in aws_lb_listener.this : k => listener.arn }
}

# Application configuration output
output "load_balancer_configuration" {
  description = "Configuration values for applications using these load balancers."
  value = {
    load_balancer_names = { for k, lb in aws_lb.this : k => lb.name }
    load_balancer_arns  = { for k, lb in aws_lb.this : k => lb.arn }
    load_balancer_dns_names = { for k, lb in aws_lb.this : k => lb.dns_name }
    load_balancer_urls = {
      for k, lb in aws_lb.this : k => "https://${lb.dns_name}"
    }
    target_group_arns = { for k, tg in aws_lb_target_group.this : k => tg.arn }
    listener_arns     = { for k, listener in aws_lb_listener.this : k => listener.arn }
  }
}

# Convenience outputs for common use cases
output "application_load_balancers" {
  description = "Map of Application Load Balancer keys to their details."
  value = {
    for k, lb in aws_lb.this : k => {
      name     = lb.name
      arn      = lb.arn
      dns_name = lb.dns_name
      zone_id  = lb.zone_id
    } if lb.load_balancer_type == "application"
  }
}

output "network_load_balancers" {
  description = "Map of Network Load Balancer keys to their details."
  value = {
    for k, lb in aws_lb.this : k => {
      name     = lb.name
      arn      = lb.arn
      dns_name = lb.dns_name
      zone_id  = lb.zone_id
    } if lb.load_balancer_type == "network"
  }
}

output "internet_facing_load_balancers" {
  description = "Map of internet-facing load balancer keys to their details."
  value = {
    for k, lb in aws_lb.this : k => {
      name     = lb.name
      arn      = lb.arn
      dns_name = lb.dns_name
      zone_id  = lb.zone_id
    } if lb.scheme == "internet-facing"
  }
}

output "internal_load_balancers" {
  description = "Map of internal load balancer keys to their details."
  value = {
    for k, lb in aws_lb.this : k => {
      name     = lb.name
      arn      = lb.arn
      dns_name = lb.dns_name
      zone_id  = lb.zone_id
    } if lb.scheme == "internal"
  }
}

# Target group outputs by load balancer
output "target_groups_by_load_balancer" {
  description = "Target groups organized by load balancer."
  value = {
    for lb_name in keys(local.load_balancer_map) : lb_name => {
      for tg_key, tg in aws_lb_target_group.this : 
        split("-", tg_key)[1] => {
          name = tg.name
          arn  = tg.arn
        } if startswith(tg_key, "${lb_name}-")
    }
  }
}

# Listener outputs by load balancer
output "listeners_by_load_balancer" {
  description = "Listeners organized by load balancer."
  value = {
    for lb_name in keys(local.load_balancer_map) : lb_name => {
      for listener_key, listener in aws_lb_listener.this : 
        listener.port => {
          arn      = listener.arn
          protocol = listener.protocol
        } if startswith(listener_key, "${lb_name}-")
    }
  }
}
