# sparrow-tofu-modules/components/aws/loadbalancer/variables.tf

variable "name_prefix" {
  description = "A prefix for all resource names to ensure uniqueness and grouping. If not provided, only the load balancer name will be used."
  type        = string
  default     = ""
  nullable    = false
}

variable "tags" {
  description = "A map of tags to assign to all created resources."
  type        = map(string)
  default     = {}
  nullable    = false
}

variable "load_balancers" {
  description = "List of load balancers to create."
  type = list(object({
    name               = string
    load_balancer_type = string # "application" or "network"
    scheme             = optional(string, "internet-facing") # "internet-facing" or "internal"
    ip_address_type    = optional(string, "ipv4") # "ipv4" or "dualstack"
    
    # Network configuration
    subnets                          = list(string)
    security_groups                  = optional(list(string), [])
    enable_deletion_protection       = optional(bool, false)
    enable_cross_zone_load_balancing = optional(bool, false)
    enable_http2                     = optional(bool, true)
    enable_waf_fail_open            = optional(bool, false)
    
    # Access logs configuration
    access_logs = optional(object({
      enabled = bool
      bucket  = string
      prefix  = optional(string, "")
    }))
    
    # Connection logs configuration (NLB only)
    connection_logs = optional(object({
      enabled = bool
      bucket  = string
      prefix  = optional(string, "")
    }))
    
    # Idle timeout (ALB only)
    idle_timeout = optional(number, 60)
    
    # Target groups
    target_groups = optional(list(object({
      name                 = string
      port                 = number
      protocol             = string # "HTTP", "HTTPS", "TCP", "TLS", "UDP", "TCP_UDP", "GENEVE"
      target_type          = optional(string, "instance") # "instance", "ip", "lambda", "alb"
      vpc_id               = string
      
      # Health check configuration
      health_check = optional(object({
        enabled             = optional(bool, true)
        healthy_threshold   = optional(number, 3)
        unhealthy_threshold = optional(number, 3)
        timeout             = optional(number, 5)
        interval            = optional(number, 30)
        path                = optional(string, "/")
        matcher             = optional(string, "200")
        port                = optional(string, "traffic-port")
        protocol            = optional(string, "HTTP")
      }))
      
      # Stickiness configuration
      stickiness = optional(object({
        enabled         = bool
        type            = string # "lb_cookie", "app_cookie", "source_ip"
        cookie_duration = optional(number, 86400)
        cookie_name     = optional(string)
      }))
      
      # Target group attributes
      deregistration_delay              = optional(number, 300)
      slow_start                       = optional(number, 0)
      load_balancing_algorithm_type    = optional(string, "round_robin")
      preserve_client_ip               = optional(bool, false)
      proxy_protocol_v2                = optional(bool, false)
      
      # Targets
      targets = optional(list(object({
        id               = string
        port             = optional(number)
        availability_zone = optional(string)
      })), [])
      
      tags = optional(map(string), {})
    })), [])
    
    # Listeners
    listeners = optional(list(object({
      port              = number
      protocol          = string # "HTTP", "HTTPS", "TCP", "TLS", "UDP", "TCP_UDP"
      ssl_policy        = optional(string)
      certificate_arn   = optional(string)
      alpn_policy       = optional(string)
      
      # Default action
      default_action = object({
        type             = string # "forward", "redirect", "fixed-response", "authenticate-cognito", "authenticate-oidc"
        target_group_arn = optional(string)
        
        # Forward action
        forward = optional(object({
          target_groups = list(object({
            arn    = string
            weight = optional(number, 100)
          }))
          stickiness = optional(object({
            enabled  = bool
            duration = number
          }))
        }))
        
        # Redirect action
        redirect = optional(object({
          host        = optional(string, "#{host}")
          path        = optional(string, "/#{path}")
          port        = optional(string, "#{port}")
          protocol    = optional(string, "#{protocol}")
          query       = optional(string, "#{query}")
          status_code = string # "HTTP_301" or "HTTP_302"
        }))
        
        # Fixed response action
        fixed_response = optional(object({
          content_type = string
          message_body = optional(string)
          status_code  = string
        }))
        
        # Authenticate Cognito action
        authenticate_cognito = optional(object({
          user_pool_arn       = string
          user_pool_client_id = string
          user_pool_domain    = string
          authentication_request_extra_params = optional(map(string))
          on_unauthenticated_request          = optional(string, "authenticate")
          scope                               = optional(string, "openid")
          session_cookie_name                 = optional(string, "AWSELBAuthSessionCookie")
          session_timeout                     = optional(number, 604800)
        }))
        
        # Authenticate OIDC action
        authenticate_oidc = optional(object({
          authorization_endpoint = string
          client_id             = string
          client_secret         = string
          issuer                = string
          token_endpoint        = string
          user_info_endpoint    = string
          authentication_request_extra_params = optional(map(string))
          on_unauthenticated_request          = optional(string, "authenticate")
          scope                               = optional(string, "openid")
          session_cookie_name                 = optional(string, "AWSELBAuthSessionCookie")
          session_timeout                     = optional(number, 604800)
        }))
      })
      
      tags = optional(map(string), {})
    })), [])
    
    tags = optional(map(string), {})
  }))
  description = "List of load balancers to create"

  validation {
    condition     = length(var.load_balancers) == length(distinct([for lb in var.load_balancers : lb.name]))
    error_message = "Load balancer names must be unique within the list."
  }

  validation {
    condition = alltrue([
      for lb in var.load_balancers : length(lb.name) > 0 && length(lb.name) <= 32
    ])
    error_message = "Load balancer name must be between 1 and 32 characters."
  }

  validation {
    condition = alltrue([
      for lb in var.load_balancers : contains(["application", "network"], lb.load_balancer_type)
    ])
    error_message = "Load balancer type must be either 'application' or 'network'."
  }

  validation {
    condition = alltrue([
      for lb in var.load_balancers : contains(["internet-facing", "internal"], lb.scheme)
    ])
    error_message = "Load balancer scheme must be either 'internet-facing' or 'internal'."
  }

  validation {
    condition = alltrue([
      for lb in var.load_balancers : contains(["ipv4", "dualstack"], lb.ip_address_type)
    ])
    error_message = "IP address type must be either 'ipv4' or 'dualstack'."
  }

  validation {
    condition = alltrue([
      for lb in var.load_balancers : length(lb.subnets) >= 2
    ])
    error_message = "Load balancer must be associated with at least 2 subnets."
  }

  validation {
    condition = alltrue([
      for lb in var.load_balancers : lb.load_balancer_type != "application" || length(lb.security_groups) > 0
    ])
    error_message = "Application load balancers must have at least one security group."
  }

  validation {
    condition = alltrue([
      for lb in var.load_balancers : lb.idle_timeout >= 1 && lb.idle_timeout <= 4000
    ])
    error_message = "Idle timeout must be between 1 and 4000 seconds."
  }
}
