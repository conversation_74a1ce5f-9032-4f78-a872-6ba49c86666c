output "certificate_arn" {
  value       = { for key, cert in aws_acm_certificate.this : key => cert.arn }
  description = "The ARN of the certificates."
}

output "records" {
  value = {
    for key, cert in aws_acm_certificate.this : cert.domain_name => {
      for option in cert.domain_validation_options : option.domain_name => {
        key   = option.resource_record_name
        value = option.resource_record_value
      }
    }
  }
  description = "A map of domain names to their DNS validation records, where each domain contains a map of record names to their validation details."
}
