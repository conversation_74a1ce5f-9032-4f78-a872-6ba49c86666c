locals {
  cerficate_map = {
    for idx, cert in var.certificates : cert.domain_name => cert
  }
}

resource "aws_acm_certificate" "this" {
  for_each = local.cerficate_map

  domain_name               = each.value.domain_name
  validation_method         = "DNS"
  key_algorithm             = each.value.key_algorithm
  subject_alternative_names = toset(each.value.subject_alternative_names)

  tags = merge(var.tags, {
    "Name" = "${var.name_prefix}-${each.value.name}-cert"
  })

  options {
    certificate_transparency_logging_preference = "ENABLED"
  }

  lifecycle {
    create_before_destroy = true
  }
}
