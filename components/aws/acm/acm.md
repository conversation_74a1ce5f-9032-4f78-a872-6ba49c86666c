## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.9 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | 5.94.1 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| aws_acm_certificate.this | resource |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_certificates"></a> [certificates](#input\_certificates) | List of certificates to create | <pre>list(object({<br/>    name                      = string // Name for tagging the certificate<br/>    domain_name               = string<br/>    subject_alternative_names = optional(list(string), [])<br/>    key_algorithm             = optional(string, "RSA_2048")<br/>  }))</pre> | `[]` | no |
| <a name="input_name_prefix"></a> [name\_prefix](#input\_name\_prefix) | Prefix to be used for all resource names | `string` | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | Tags to be applied to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_certificate_arn"></a> [certificate\_arn](#output\_certificate\_arn) | The ARN of the certificates. |
| <a name="output_records"></a> [records](#output\_records) | A map of domain names to their DNS validation records, where each domain contains a map of record names to their validation details. |

## Module Summary

This module manages AWS Certificate Manager (ACM) certificates to secure your AWS applications with SSL/TLS. It provides a simple, standardized method for creating and managing certificates with DNS validation.

## Features

- Creates ACM certificates with DNS validation method
- Supports creation of multiple certificates
- Enforces unique domain names across certificates
- Supports subject alternative names (SANs) for multi-domain certificates
- Configurable key algorithm with default RSA_2048
- Automatically enables certificate transparency logging
- Uses create_before_destroy lifecycle policy to ensure zero downtime during certificate renewal
- Consistent resource naming with configurable prefix
- Flexible tagging system for resource organization

## Usage Example

```hcl
module "acm" {
  source = "sparrow-tofu-modules/components/aws/acm"

  name_prefix = "myapp-prod"

  certificates = [
    {
      name        = "main"
      domain_name = "example.com"
      subject_alternative_names = ["www.example.com", "api.example.com"]
    },
    {
      name        = "subdomain"
      domain_name = "app.example.com"
      key_algorithm = "RSA_4096"  # Optional: Override default key algorithm
    }
  ]

  tags = {
    Environment = "production"
    Project     = "MyApplication"
    ManagedBy   = "Terraform"
  }
}

# After applying, use the DNS validation records to create entries in your DNS provider
output "dns_validation_records" {
  description = "Records to create in your DNS provider to validate certificate ownership"
  value       = module.acm.records
}

# Use the certificate ARNs with other AWS resources
output "certificate_arns" {
  description = "ARNs of the created certificates"
  value       = module.acm.certificate_arn
}
```

After applying this module, you must create the DNS validation records in your DNS provider to complete the certificate validation process.
