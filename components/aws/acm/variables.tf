variable "certificates" {
  type = list(object({
    name                      = string // Name for tagging the certificate
    domain_name               = string
    subject_alternative_names = optional(list(string), [])
    key_algorithm             = optional(string, "RSA_2048")
  }))

  description = "List of certificates to create"
  validation {
    condition     = length(distinct([for cert in var.certificates : cert.domain_name])) == length(var.certificates)
    error_message = "Domain names must be unique across all certificates."
  }
}

variable "name_prefix" {
  type        = string
  description = "Prefix to be used for all resource names"
}

variable "tags" {
  type        = map(string)
  description = "Tags to be applied to all resources"
}
