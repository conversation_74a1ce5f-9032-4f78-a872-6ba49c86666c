# Sparrow IAC Modules
This repository contains all the modules that can be used by the developers to create and manage the infrastructure.
It primarily focuses on creating infrastructure modules tailored specifically to the policies of our organization.
This will be used by a iac repository where we maintain the configuration for each and every resources created through terragrunt

# Directory structure
```
- repo-root
  - components
    - aws/
      - vpc
        - main.tf
        - variables.tf
        - output.tf
        - ...
      - rds
        - main.tf
        - variables.tf
        - output.tf
        - ...
    - pinecone/
      - main.tf
      - variables.tf
      - outputs.tf
      - pinecone.md
      - ...
  - services
    - aws/
      - blink
        - main.tf
        - variables.tf
        - output.tf
        - ...
      - app-v1
        - main.tf
        - variables.tf
        - output.tf
        - ...
      - vector-search-app
        - main.tf
        - variables.tf
        - outputs.tf
        - examples/
        - ...
  - legacy
    - surveysparrow
      - vpc/
        - main.tf
        - variables.tf
        - output.tf
        - ...
    - thrivesparrow
      - rds/
        - main.tf
        - variables.tf
        - output.tf
```

# Setting up the Repository
1. Clone the repository
2. Install dependencies
```
brew install opentofu pre-commit tflint trivy terraform-docs
```
3. Verify Installation using the `--version` flag for the commands
```
tofu --version
pre-commit --version
tflint --version
trivy --version
terraform-docs --version
```
4. Setup pre-commit hooks from within the repository
```
pre-commit install
```
5. For testing resource creation, use the temporary keys generated from the AWS Start console.
6. Export those keys from the terminal and you can run any tofu commands.

# Creating a new Component module
1. Components are core AWS Services.
2. It should be as generalized as possible and should be tailored as per the policies of our organization.
3. Use public modules only when it is flexible and can allow a developer to get information from the parameters.
4. Donot lock versions / provider inside this repository. (This should be controlled from the config repository.)


# Creating a new Service module
1. Services are those used in our Organization which relies on the AWS infrastructure.
2. This should be a pulling all the modules from the core components.
3. These modules should be flexible enough to use shared resources if necessary.
4. It might create dedicated resource as well if necessary.

# Note on Legacy modules
1. Legacy modules exist for the sole purpose of importing existing resources into IAC and import existing unmodularized terraform code into the repository.
2. There shouldn't be any create action performed for legacy modules, it's purpose is to only maintain the state of existing resources that were created manually.
3. Only updates are permitted for the Legacy modules.

# Module workflow
1. Create a module.
2. Create a tag to lock the version for the module.
3. Tags should be created only when the module is merged to master.
4. Use the specific tags in the Terragrunt configuration instead of branches.
5. Follow the tag format for each module - `{module_name}-v{semantic_versioning}`
6. In terragrunt configuration, you can import each module as follows,
```
terraform {
  source = "git::*****************:surveysparrow/sparrow-iac-modules.git//components/aws/{module}?ref={module}-v{semantic_version}"
}
```
# Guide on writing a new module
1. For smaller and neglible cost resources, consider writing modules which take a list as input.
2. Use for_each to name the resources in the state file. So state manipulation will be easier.
3. Follow consistent naming convention, `<service-name>-<feature-name>-<aws-service-name>`. For eg. `ss-app-v1-rds-cluster`, `reputation-job-alerts-queue`
4. Follow consistent tagging across resources. Never set empty defaults for tags.
5. Use files for IAM policies and read them from the directory, never code IAM policies inside the module.
6. Try to use data resource for cases where data needs to be copied from AWS. If a data resource is present, use it do not hard code it.
7. Think extensibility while writing a module, the updates, upgrades should be easier without messing with the state file or resource recreation.
8. User shouldn't have all the options exposed, the module should follow two things -> Secure by default, Optimised for cost.
